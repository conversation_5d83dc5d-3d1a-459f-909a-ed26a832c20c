"""
Volatility Statistics Tab for Market Odds and Options Analyzer

This module provides a tab that contains three subtabs:
1. Volatility Graph
2. Density Graph
3. FWL Odds
"""

from PyQt6 import QtWidgets, QtCore, QtGui
from volatility_graph import VolatilityGraphTab
from density_graph import DensityGraphTab
from fwl_odds import FWLOddsTab
import logging
import time
import datetime
import pandas as pd
from lru_cache import LRUCache
import pyqtgraph as pg

# Set up logger
logger = logging.getLogger(__name__)

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
        'highlight': '#FFC107',            # Material Design Amber
        'selection': '#2979FF',            # Selection highlight color
        'button_radius': '4px',            # Button corner radius
        'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
    }

class VolatilityStatisticsTab(QtWidgets.QWidget):
    """
    Main tab that contains three subtabs: Volatility Graph, Density Graph, and FWL Odds.
    """
    def __init__(self, parent=None, data_tab=None):
        """
        Initialize the Volatility Statistics tab.

        Args:
            parent: Parent widget
            data_tab: Reference to the Data tab
        """
        super().__init__(parent)
        self.parent = parent
        self.data_tab = data_tab

        # Cache for tab data to avoid unnecessary regeneration
        self.tab_data_cache = LRUCache(capacity=10)

        # Track the last update time for each tab to prevent rapid refreshes
        self.last_update_time = {}

        # Minimum time between updates in milliseconds - increased to reduce choppy behavior
        self.min_update_interval = 1000  # Increased from 500ms to 1000ms

        # Flag to track if an update is in progress
        self.update_in_progress = False

        # Timer for debouncing updates
        self.update_timer = QtCore.QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._delayed_generate_tab)

        # Dictionary to track which statistics are toggled on for plotting
        self.toggled_stats = {}

        # AK's weekly vol zones state and data
        self.ak_weekly_vol_zones_active = False
        self.ak_fwl5_levels = {}
        self.ak_weekday_levels = {}

        # AK's daily vol zones state and data
        self.ak_daily_vol_zones_active = False
        self.ak_daily_vol_zones_data = {}
        self.ak_daily_vol_zones_cache = {}  # Cache for volatility graphs

        # AK's odds zones state and data
        self.ak_odds_zones_active = False
        self.ak_odds_zones_data = {}
        self.ak_odds_zones_cache = {}  # Cache for odds zones

        # AK's density zones state and data
        self.ak_density_zones_active = False
        self.ak_density_zones_cache = {}  # Cache for density zones data

        # Store the stats data for use by the graph tabs
        self.stats_data = []

        # Flag to track if we're viewing historical data
        self.viewing_historical = False

        # Store the original data when viewing historical data
        self.original_data = None

        # Store the original rebased_data when viewing historical data
        self.original_rebased_data = None

        # Store the original categories when viewing historical data
        self.original_categories = None

        # Store the historical data timestamp for display
        self.historical_timestamp = None

        # Store the historical index (cutoff point for analysis)
        self.historical_index = None

        # Store the historical day's high and low prices for chart display
        self.historical_day_high = None
        self.historical_day_low = None

        # Reference to the back to current button (will be created in init_ui)
        self.back_to_current_button = None

        # Initialize UI
        self.init_ui()

        # Connect to the data_tab's refresh_data method to update all substabs when data is fetched
        if self.data_tab:
            # Connect to the data_tab's refresh_data method
            self.data_tab.refresh_data = self.wrap_refresh_data(self.data_tab.refresh_data)

        # Connect to the universal_controls and market_odds_tab data_fetched signals if available
        try:
            # Find the main window instance
            for widget in QtWidgets.QApplication.topLevelWidgets():
                # Connect to universal_controls data_fetched signal
                if hasattr(widget, 'universal_controls'):
                    widget.universal_controls.data_fetched.connect(self.on_data_fetched_universal)
                    logger.info("Connected to universal_controls data_fetched signal")

                # Connect to market_odds_tab data_fetched signal
                if hasattr(widget, 'market_odds_tab'):
                    widget.market_odds_tab.data_fetched.connect(self.on_data_fetched_market_odds)
                    logger.info("Connected to market_odds_tab data_fetched signal")
        except Exception as e:
            logger.warning(f"Could not connect to data_fetched signals: {str(e)}")

        # Connect to parameter registry signals to detect changes in timeframe and vector length
        # but we won't automatically update all substabs when these change
        from parameter_registry import default_registry
        default_registry.ui_parameter_changed.connect(self.on_parameter_changed)

    def create_stat_row(self, label_text, value_text="--", count_text="--", winrate_text="--"):
        """Create a row with three columns for statistics display and a toggle checkbox

        Args:
            label_text (str): The label for the statistic
            value_text (str): The value for the statistic
            count_text (str): The count value
            winrate_text (str): The win rate value

        Returns:
            QWidget: A widget containing the row with three columns and a toggle
        """
        # Create a container widget for the row
        row_widget = QtWidgets.QWidget()
        row_layout = QtWidgets.QHBoxLayout(row_widget)
        row_layout.setContentsMargins(0, 0, 0, 0)
        row_layout.setSpacing(10)

        # Create a toggle checkbox
        toggle_checkbox = QtWidgets.QCheckBox()
        toggle_checkbox.setChecked(False)
        toggle_checkbox.setFixedWidth(20)
        toggle_checkbox.setStyleSheet(f"""
            QCheckBox::indicator {{
                width: 15px;
                height: 15px;
                border-radius: 3px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {THEME_COLORS['primary_accent']};
                border: 2px solid {THEME_COLORS['text']};
            }}
            QCheckBox::indicator:unchecked {{
                background-color: {THEME_COLORS['background']};
                border: 2px solid {THEME_COLORS['borders']};
            }}
        """)

        # Store the stat_id in the checkbox for reference
        stat_id = label_text.replace(" ", "_").lower()
        toggle_checkbox.setProperty("stat_id", stat_id)

        # Connect the toggle to the handler
        toggle_checkbox.toggled.connect(self.on_stat_toggle_changed)

        # Create labels for each column
        stat_label = QtWidgets.QLabel(f"{label_text}: {value_text}")
        count_label = QtWidgets.QLabel(count_text)
        winrate_label = QtWidgets.QLabel(winrate_text)

        # Store references to the labels and toggle for later updates
        row_widget.stat_label = stat_label
        row_widget.count_label = count_label
        row_widget.winrate_label = winrate_label
        row_widget.toggle_checkbox = toggle_checkbox
        row_widget.stat_id = stat_id

        # Style the labels
        label_style = f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace;"
        stat_label.setStyleSheet(label_style)
        count_label.setStyleSheet(label_style)
        winrate_label.setStyleSheet(label_style)

        # Enable word wrapping
        stat_label.setWordWrap(True)

        # Add toggle and labels to the row layout with appropriate stretch factors
        row_layout.addWidget(toggle_checkbox, 0)  # No stretch for checkbox
        row_layout.addWidget(stat_label, 3)  # Larger width for statistics
        row_layout.addWidget(count_label, 1)
        row_layout.addWidget(winrate_label, 1)

        return row_widget

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout with minimal margins
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 10)  # Add bottom margin of 10px
        main_layout.setSpacing(0)  # Remove spacing between widgets

        # Create a horizontal layout for the tab widget and statistics box
        content_layout = QtWidgets.QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Create a vertical layout for the tab widget and the tab bar extras
        tab_layout = QtWidgets.QVBoxLayout()
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)  # No spacing between elements to position chart right below tabs

        # Create a horizontal layout for the tab bar and historical data dropdown
        tab_bar_layout = QtWidgets.QHBoxLayout()
        tab_bar_layout.setContentsMargins(0, 0, 0, 0)
        tab_bar_layout.setSpacing(10)

        # Create a stacked widget to hold the subtabs
        self.stacked_widget = QtWidgets.QStackedWidget()
        self.stacked_widget.setStyleSheet(f"""
            QStackedWidget {{
                border: 1px solid {THEME_COLORS['borders']};
                background-color: {THEME_COLORS['background']};
                border-radius: 0px;
                margin: 0px;
                padding: 0px;
            }}
        """)

        # Create a button group for the radio buttons
        self.tab_button_group = QtWidgets.QButtonGroup(self)

        # Create a widget to hold the radio buttons
        self.tab_buttons_widget = QtWidgets.QWidget()
        self.tab_buttons_layout = QtWidgets.QHBoxLayout(self.tab_buttons_widget)
        self.tab_buttons_layout.setContentsMargins(10, 5, 10, 5)  # Reduced vertical margins
        self.tab_buttons_layout.setSpacing(20)  # Increased spacing between buttons
        self.tab_buttons_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)  # Center the buttons

        # Radio button style for chart selection
        radio_style = f"""
            QRadioButton {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 13px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QRadioButton:hover {{
                background: transparent;
            }}
            QRadioButton:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QRadioButton::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 8px;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid white;
            }}
            QRadioButton::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QRadioButton::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Create a custom tab bar container to hold the tab bar and historical data dropdown
        self.tab_bar_container = QtWidgets.QWidget()
        self.tab_bar_container.setFixedHeight(0)  # Set height to 0 to eliminate black space
        self.tab_bar_container.setStyleSheet(f"background-color: {THEME_COLORS['background']}; border: none; margin: 0; padding: 0;")
        self.tab_bar_container.setVisible(False)  # Hide the container since it's not being used

        # Create a horizontal layout for the tab bar container
        tab_bar_container_layout = QtWidgets.QHBoxLayout(self.tab_bar_container)
        tab_bar_container_layout.setContentsMargins(0, 0, 0, 0)
        tab_bar_container_layout.setSpacing(5)  # Reduced spacing

        # Add a spacer to push any future elements to the right
        tab_bar_container_layout.addStretch(1)

        # Create the subtabs
        self.volatility_graph_tab = VolatilityGraphTab(self, data_tab=self.data_tab)
        self.density_graph_tab = DensityGraphTab(self, data_tab=self.data_tab)
        self.fwl_odds_tab = FWLOddsTab(self, data_tab=self.data_tab)

        # Create radio buttons for each tab
        self.volatility_graph_btn = QtWidgets.QRadioButton("Volatility Graph")
        self.volatility_graph_btn.setStyleSheet(radio_style)
        self.volatility_graph_btn.setChecked(True)  # Default selected
        self.tab_button_group.addButton(self.volatility_graph_btn, 0)
        self.tab_buttons_layout.addWidget(self.volatility_graph_btn)

        self.density_graph_btn = QtWidgets.QRadioButton("Density Graph")
        self.density_graph_btn.setStyleSheet(radio_style)
        self.tab_button_group.addButton(self.density_graph_btn, 1)
        self.tab_buttons_layout.addWidget(self.density_graph_btn)

        self.fwl_odds_btn = QtWidgets.QRadioButton("FWL Odds")
        self.fwl_odds_btn.setStyleSheet(radio_style)
        self.tab_button_group.addButton(self.fwl_odds_btn, 2)
        self.tab_buttons_layout.addWidget(self.fwl_odds_btn)

        # Add the subtabs to the stacked widget
        self.stacked_widget.addWidget(self.volatility_graph_tab)
        self.stacked_widget.addWidget(self.density_graph_tab)
        self.stacked_widget.addWidget(self.fwl_odds_tab)

        # Connect button group signal to change the stacked widget index
        self.tab_button_group.idClicked.connect(self.stacked_widget.setCurrentIndex)
        self.tab_button_group.idClicked.connect(self.on_tab_changed)

        # Create a non-scrollable panel above the statistics box
        self.non_scroll_panel = QtWidgets.QWidget()
        self.non_scroll_panel.setFixedWidth(700)  # Same width as stats box
        self.non_scroll_panel.setStyleSheet(f"background-color: {THEME_COLORS['background']}; border: 1px solid {THEME_COLORS['borders']}; border-bottom: none;")

        # Create a layout for the non-scrollable panel
        non_scroll_layout = QtWidgets.QVBoxLayout(self.non_scroll_panel)
        non_scroll_layout.setContentsMargins(5, 5, 5, 5)

        # Create a visible separator at the top of the settings panel
        top_separator = QtWidgets.QFrame()
        top_separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        top_separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)  # Changed to Sunken for better appearance
        top_separator.setFixedHeight(2)  # Slightly thicker for visibility
        top_separator.setStyleSheet(f"background-color: {THEME_COLORS['borders']}; border: none;")
        non_scroll_layout.addWidget(top_separator)

        # Add a small spacer after the separator
        non_scroll_layout.addSpacing(3)

        # Create a collapsible header for the settings panel
        self.settings_header = QtWidgets.QWidget()
        self.settings_header.setStyleSheet(f"background-color: {THEME_COLORS['control_panel']}; border: none;")
        self.settings_header.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)

        settings_header_layout = QtWidgets.QHBoxLayout(self.settings_header)
        settings_header_layout.setContentsMargins(10, 5, 10, 5)
        settings_header_layout.setSpacing(5)

        # Add collapse/expand arrow
        self.collapse_arrow = QtWidgets.QLabel("▼")
        self.collapse_arrow.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
        self.collapse_arrow.setFixedWidth(20)

        # Add title
        self.settings_title = QtWidgets.QLabel("Settings")
        self.settings_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; border: none;")

        settings_header_layout.addWidget(self.collapse_arrow)
        settings_header_layout.addWidget(self.settings_title)
        settings_header_layout.addStretch()

        # Make the header clickable
        self.settings_header.mousePressEvent = self.toggle_settings_panel

        non_scroll_layout.addWidget(self.settings_header)

        # Create a collapsible container for the settings content
        self.settings_content = QtWidgets.QWidget()
        self.settings_content.setVisible(True)  # Start expanded

        settings_content_layout = QtWidgets.QVBoxLayout(self.settings_content)
        settings_content_layout.setContentsMargins(0, 0, 0, 0)
        settings_content_layout.setSpacing(0)

        # Create a horizontal layout to hold the two halves of the settings panel
        settings_split_layout = QtWidgets.QHBoxLayout()
        settings_split_layout.setContentsMargins(0, 0, 0, 0)
        settings_split_layout.setSpacing(0)  # No spacing between panels

        # Create left and right panels
        left_panel = QtWidgets.QWidget()
        right_panel = QtWidgets.QWidget()

        # Create layouts for the panels with absolutely minimal margins
        left_layout = QtWidgets.QVBoxLayout(left_panel)
        left_layout.setContentsMargins(8, 5, 0, 5)  # No right margin
        left_layout.setSpacing(5)  # Reduce spacing between elements

        right_layout = QtWidgets.QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 5, 8, 5)  # No left margin
        right_layout.setSpacing(5)  # Reduce spacing between elements

        # Add subtitle to the left panel
        left_subtitle = QtWidgets.QLabel("Forward Looking Aggregation")
        left_subtitle.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 12px; padding: 3px; border-bottom: 1px solid {THEME_COLORS['borders']};")
        left_subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(left_subtitle)

        # Add some spacing (reduced)
        left_layout.addSpacing(3)

        # Create a cleaner numeric input with plus/minus buttons for FWL Aggregation
        numeric_input_container = QtWidgets.QWidget()
        numeric_input_container.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
            }}
        """)
        numeric_input_layout = QtWidgets.QHBoxLayout(numeric_input_container)
        numeric_input_layout.setContentsMargins(8, 6, 8, 6)  # Increased padding for cleaner look
        numeric_input_layout.setSpacing(8)  # Increased spacing

        # Create a label for the input
        numeric_input_label = QtWidgets.QLabel("FWL Aggregation:")  # Full name for clarity
        numeric_input_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; background: transparent; border: none;")

        # Create the numeric input field
        self.numeric_input = QtWidgets.QLineEdit("1")  # Start with 1 as the minimum value
        self.numeric_input.setFixedWidth(40)  # Slightly smaller
        self.numeric_input.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.numeric_input.setStyleSheet(f"""
            QLineEdit {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 3px;
                font-family: 'Consolas', 'Courier New', monospace;
            }}
        """)
        # Only allow integer input between 1 and 10
        int_validator = QtGui.QIntValidator(1, 10)
        self.numeric_input.setValidator(int_validator)

        # Create a container for the buttons
        button_container = QtWidgets.QWidget()
        button_container.setStyleSheet("background: transparent; border: none;")
        button_layout = QtWidgets.QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(4)

        # Create plus button
        plus_button = QtWidgets.QPushButton("+")
        plus_button.setFixedSize(24, 24)
        plus_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                font-weight: bold;
                padding: 0px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)

        # Create minus button
        minus_button = QtWidgets.QPushButton("-")
        minus_button.setFixedSize(24, 24)
        minus_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                font-weight: bold;
                padding: 0px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)

        # Add buttons to the button container
        button_layout.addWidget(minus_button)
        button_layout.addWidget(plus_button)

        # Connect button signals to slots
        plus_button.clicked.connect(self.increment_numeric_value)
        minus_button.clicked.connect(self.decrement_numeric_value)

        # Connect numeric input text changed signal to update all substabs
        self.numeric_input.textChanged.connect(self.on_numeric_input_changed)

        # Add widgets to the layout
        numeric_input_layout.addWidget(numeric_input_label)
        numeric_input_layout.addStretch(1)  # Push input and buttons to the right
        numeric_input_layout.addWidget(self.numeric_input)
        numeric_input_layout.addWidget(button_container)

        # Add the numeric input container to the left panel
        left_layout.addWidget(numeric_input_container)

        # Add some spacing (reduced)
        left_layout.addSpacing(5)

        # Create a cleaner numeric input with plus/minus buttons for Occurrence Count
        occurrence_input_container = QtWidgets.QWidget()
        occurrence_input_container.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
            }}
        """)
        occurrence_input_layout = QtWidgets.QHBoxLayout(occurrence_input_container)
        occurrence_input_layout.setContentsMargins(8, 6, 8, 6)  # Increased padding for cleaner look
        occurrence_input_layout.setSpacing(8)  # Increased spacing

        # Create a label for the occurrence count input
        occurrence_input_label = QtWidgets.QLabel("Occurrences:")
        occurrence_input_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; background: transparent; border: none;")

        # Create the occurrence count input field
        self.occurrence_input = QtWidgets.QLineEdit("0")  # Start with 0 as the default (all occurrences)
        self.occurrence_input.setFixedWidth(40)  # Slightly smaller
        self.occurrence_input.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.occurrence_input.setStyleSheet(f"""
            QLineEdit {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 3px;
                font-family: 'Consolas', 'Courier New', monospace;
            }}
        """)
        # Only allow integer input between 0 and 100
        occurrence_validator = QtGui.QIntValidator(0, 100)
        self.occurrence_input.setValidator(occurrence_validator)

        # Create a container for the buttons
        occurrence_button_container = QtWidgets.QWidget()
        occurrence_button_container.setStyleSheet("background: transparent; border: none;")
        occurrence_button_layout = QtWidgets.QHBoxLayout(occurrence_button_container)
        occurrence_button_layout.setContentsMargins(0, 0, 0, 0)
        occurrence_button_layout.setSpacing(4)

        # Create plus button for occurrence count
        occurrence_plus_button = QtWidgets.QPushButton("+")
        occurrence_plus_button.setFixedSize(24, 24)
        occurrence_plus_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                font-weight: bold;
                padding: 0px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)

        # Create minus button for occurrence count
        occurrence_minus_button = QtWidgets.QPushButton("-")
        occurrence_minus_button.setFixedSize(24, 24)
        occurrence_minus_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                font-weight: bold;
                padding: 0px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)

        # Add buttons to the button container
        occurrence_button_layout.addWidget(occurrence_minus_button)
        occurrence_button_layout.addWidget(occurrence_plus_button)

        # No fetch button needed anymore since we update automatically

        # Connect button signals to slots
        occurrence_plus_button.clicked.connect(self.increment_occurrence_value)
        occurrence_minus_button.clicked.connect(self.decrement_occurrence_value)

        # Note: We no longer connect textChanged to automatically update - user must press apply button

        # Add widgets to the layout
        occurrence_input_layout.addWidget(occurrence_input_label)
        occurrence_input_layout.addStretch(1)  # Push input and buttons to the right
        occurrence_input_layout.addWidget(self.occurrence_input)
        occurrence_input_layout.addWidget(occurrence_button_container)

        # Add the occurrence input container to the left panel
        left_layout.addWidget(occurrence_input_container)

        # Add some spacing
        left_layout.addSpacing(3)

        # Create a container for the occurrence control buttons
        occurrence_buttons_container = QtWidgets.QWidget()
        occurrence_buttons_container.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['background']};
                border: none;
                border-radius: 4px;
            }}
        """)
        occurrence_buttons_layout = QtWidgets.QHBoxLayout(occurrence_buttons_container)
        occurrence_buttons_layout.setContentsMargins(8, 6, 8, 6)
        occurrence_buttons_layout.setSpacing(8)

        # Create "Apply Occurrence Limit" button
        self.apply_occurrence_button = QtWidgets.QPushButton("Apply Occurrence Limit")
        self.apply_occurrence_button.setFixedHeight(28)
        self.apply_occurrence_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        self.apply_occurrence_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 3px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """)
        self.apply_occurrence_button.clicked.connect(self.apply_occurrence_limit)

        # Create "Reset" button
        self.reset_occurrence_button = QtWidgets.QPushButton("Reset")
        self.reset_occurrence_button.setFixedHeight(28)
        self.reset_occurrence_button.setFixedWidth(60)
        self.reset_occurrence_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        self.reset_occurrence_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 3px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """)
        self.reset_occurrence_button.clicked.connect(self.reset_occurrence_limit)

        # Add buttons to the layout
        occurrence_buttons_layout.addWidget(self.apply_occurrence_button, 1)  # Give apply button more space
        occurrence_buttons_layout.addWidget(self.reset_occurrence_button, 0)  # Fixed width for reset

        # Add the buttons container to the left panel
        left_layout.addWidget(occurrence_buttons_container)

        # Add some spacing
        left_layout.addSpacing(5)

        # AK's weekly vol zones button
        ak_zones_container = QtWidgets.QWidget()
        ak_zones_container.setStyleSheet(f"background-color: {THEME_COLORS['control_panel']}; border: 1px solid {THEME_COLORS['borders']}; border-radius: 5px; padding: 5px;")
        ak_zones_layout = QtWidgets.QVBoxLayout(ak_zones_container)
        ak_zones_layout.setContentsMargins(5, 5, 5, 5)
        ak_zones_layout.setSpacing(5)

        # AK zones label
        ak_zones_label = QtWidgets.QLabel("AK's Weekly Vol Zones")
        ak_zones_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; border: none; padding: 0px;")
        ak_zones_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        ak_zones_layout.addWidget(ak_zones_label)

        # AK zones button
        self.ak_weekly_vol_zones_button = QtWidgets.QPushButton("Activate AK Zones")
        ak_zones_button_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 6px 12px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """
        self.ak_weekly_vol_zones_button.setStyleSheet(ak_zones_button_style)
        self.ak_weekly_vol_zones_button.clicked.connect(self.ak_weekly_vol_zones)
        ak_zones_layout.addWidget(self.ak_weekly_vol_zones_button)

        # Add the AK zones container to the left panel
        left_layout.addWidget(ak_zones_container)

        # Add some spacing
        left_layout.addSpacing(5)

        # AK's daily vol zones button
        ak_daily_zones_container = QtWidgets.QWidget()
        ak_daily_zones_container.setStyleSheet(f"background-color: {THEME_COLORS['control_panel']}; border: 1px solid {THEME_COLORS['borders']}; border-radius: 5px; padding: 5px;")
        ak_daily_zones_layout = QtWidgets.QVBoxLayout(ak_daily_zones_container)
        ak_daily_zones_layout.setContentsMargins(5, 5, 5, 5)
        ak_daily_zones_layout.setSpacing(5)

        # AK daily zones label
        ak_daily_zones_label = QtWidgets.QLabel("AK's Daily Vol Zones")
        ak_daily_zones_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; border: none; padding: 0px;")
        ak_daily_zones_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        ak_daily_zones_layout.addWidget(ak_daily_zones_label)

        # AK daily zones button
        self.ak_daily_vol_zones_button = QtWidgets.QPushButton("Activate AK Daily Zones")
        ak_daily_zones_button_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 6px 12px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """
        self.ak_daily_vol_zones_button.setStyleSheet(ak_daily_zones_button_style)
        self.ak_daily_vol_zones_button.clicked.connect(self.ak_daily_vol_zones)
        ak_daily_zones_layout.addWidget(self.ak_daily_vol_zones_button)

        # Add the AK daily zones container to the left panel
        left_layout.addWidget(ak_daily_zones_container)

        # Debug button for AK daily zones cache (temporary)
        debug_button = QtWidgets.QPushButton("Debug Cache")
        debug_button.clicked.connect(self.debug_ak_daily_cache)
        debug_button.setStyleSheet(ak_daily_zones_button_style)
        ak_daily_zones_layout.addWidget(debug_button)

        # Add some spacing
        left_layout.addSpacing(5)

        # AK's odds zones button
        ak_odds_zones_container = QtWidgets.QWidget()
        ak_odds_zones_container.setStyleSheet(f"background-color: {THEME_COLORS['control_panel']}; border: 1px solid {THEME_COLORS['borders']}; border-radius: 5px; padding: 5px;")
        ak_odds_zones_layout = QtWidgets.QVBoxLayout(ak_odds_zones_container)
        ak_odds_zones_layout.setContentsMargins(5, 5, 5, 5)
        ak_odds_zones_layout.setSpacing(5)

        # AK odds zones label
        ak_odds_zones_label = QtWidgets.QLabel("AK's Odds Zones")
        ak_odds_zones_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; border: none; padding: 0px;")
        ak_odds_zones_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        ak_odds_zones_layout.addWidget(ak_odds_zones_label)

        # AK odds zones button
        self.ak_odds_zones_button = QtWidgets.QPushButton("Activate AK Odds Zones")
        ak_odds_zones_button_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 6px 12px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """
        self.ak_odds_zones_button.setStyleSheet(ak_odds_zones_button_style)
        self.ak_odds_zones_button.clicked.connect(self.ak_odds_zones)
        ak_odds_zones_layout.addWidget(self.ak_odds_zones_button)

        # Add the AK odds zones container to the left panel
        left_layout.addWidget(ak_odds_zones_container)

        # Debug button for AK odds zones cache
        debug_odds_button = QtWidgets.QPushButton("Debug Odds Cache")
        debug_odds_button.clicked.connect(self.debug_ak_odds_cache)
        debug_odds_button.setStyleSheet(ak_odds_zones_button_style)
        ak_odds_zones_layout.addWidget(debug_odds_button)

        # AK's density zones button container
        ak_density_zones_container = QtWidgets.QWidget()
        ak_density_zones_container.setStyleSheet(f"background-color: {THEME_COLORS['control_panel']}; border: 1px solid {THEME_COLORS['borders']}; border-radius: 5px; padding: 5px;")
        ak_density_zones_layout = QtWidgets.QVBoxLayout(ak_density_zones_container)
        ak_density_zones_layout.setContentsMargins(5, 5, 5, 5)
        ak_density_zones_layout.setSpacing(5)

        # AK density zones label
        ak_density_zones_label = QtWidgets.QLabel("AK's Density Zones")
        ak_density_zones_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; border: none; padding: 0px;")
        ak_density_zones_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        ak_density_zones_layout.addWidget(ak_density_zones_label)

        # AK density zones button
        self.ak_density_zones_button = QtWidgets.QPushButton("AK's Users Choice Density Zones")
        self.ak_density_zones_button.setStyleSheet(ak_odds_zones_button_style)
        self.ak_density_zones_button.clicked.connect(self.ak_density_zones)
        ak_density_zones_layout.addWidget(self.ak_density_zones_button)

        # Add density preset buttons under AK's Density Zones
        density_preset_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10px;
                min-height: 16px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid {THEME_COLORS['secondary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['secondary_accent']};
                color: {THEME_COLORS['background']};
            }}
        """

        # Length 1 Daily 200DTL button
        self.density_200dtl_btn = QtWidgets.QPushButton("L1 Daily 200DTL")
        self.density_200dtl_btn.setStyleSheet(density_preset_style)
        self.density_200dtl_btn.setToolTip("Set Length 1, Daily timeframe, 200 days to load and activate density zones")
        self.density_200dtl_btn.clicked.connect(self.on_density_200dtl_clicked)
        ak_density_zones_layout.addWidget(self.density_200dtl_btn)

        # Length 1 Daily 500DTL button
        self.density_500dtl_btn = QtWidgets.QPushButton("L1 Daily 500DTL")
        self.density_500dtl_btn.setStyleSheet(density_preset_style)
        self.density_500dtl_btn.setToolTip("Set Length 1, Daily timeframe, 500 days to load and activate density zones")
        self.density_500dtl_btn.clicked.connect(self.on_density_500dtl_clicked)
        ak_density_zones_layout.addWidget(self.density_500dtl_btn)

        # Length 3 15k DTL button
        self.density_15k_btn = QtWidgets.QPushButton("L3 15k DTL")
        self.density_15k_btn.setStyleSheet(density_preset_style)
        self.density_15k_btn.setToolTip("Set Length 3, Daily timeframe, 15000 days to load and activate density zones")
        self.density_15k_btn.clicked.connect(self.on_density_15k_clicked)
        ak_density_zones_layout.addWidget(self.density_15k_btn)

        # Save Zones to Cache button (initially hidden)
        save_zones_button_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: #2d5a2d;
                border: 1px solid #4a8f4a;
                border-radius: 3px;
                padding: 6px 12px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: #3d6a3d;
                border: 1px solid #5a9f5a;
            }}
            QPushButton:pressed {{
                background-color: #4a8f4a;
                color: {THEME_COLORS['background']};
            }}
        """

        self.save_zones_to_cache_button = QtWidgets.QPushButton("Save Zones to Cache")
        self.save_zones_to_cache_button.setStyleSheet(save_zones_button_style)
        self.save_zones_to_cache_button.setToolTip("Save density zones to cache and plot them on the candlestick chart")
        self.save_zones_to_cache_button.clicked.connect(self.save_zones_to_cache)
        self.save_zones_to_cache_button.setVisible(False)  # Initially hidden
        ak_density_zones_layout.addWidget(self.save_zones_to_cache_button)

        # Add the AK density zones container to the left panel
        left_layout.addWidget(ak_density_zones_container)

        # Add some spacing
        left_layout.addSpacing(5)

        # Add subtitle to the right panel
        right_subtitle = QtWidgets.QLabel("Market Condition Filters")
        right_subtitle.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 12px; padding: 5px; border-bottom: 1px solid {THEME_COLORS['borders']};")
        right_subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(right_subtitle)

        # Add some spacing
        right_layout.addSpacing(5)

        # Create radio button group for Market Condition Filters
        self.filter_button_group = QtWidgets.QButtonGroup(self)

        # Radio button style
        radio_style = f"""
            QRadioButton {{
                color: {THEME_COLORS['text']};
                font-family: 'Consolas', 'Courier New', monospace;
                spacing: 5px;
                padding: 5px;
            }}
            QRadioButton::indicator {{
                width: 15px;
                height: 15px;
                border-radius: 8px;
            }}
            QRadioButton::indicator:checked {{
                background-color: {THEME_COLORS['primary_accent']};
                border: 2px solid {THEME_COLORS['text']};
            }}
            QRadioButton::indicator:unchecked {{
                background-color: {THEME_COLORS['background']};
                border: 2px solid {THEME_COLORS['borders']};
            }}
        """

        # Create custom style for matching mode radio buttons
        matching_radio_style = f"""
            QRadioButton {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 13px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QRadioButton:hover {{
                background: transparent;
            }}
            QRadioButton:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QRadioButton::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 8px;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid white;
            }}
            QRadioButton::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QRadioButton::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Create radio buttons
        self.hl_matching_btn = QtWidgets.QRadioButton("H/L Matching")
        self.hl_matching_btn.setStyleSheet(matching_radio_style)
        self.hl_matching_btn.setChecked(True)  # Set as default selected

        self.weekday_matching_btn = QtWidgets.QRadioButton("Weekday Matching")
        self.weekday_matching_btn.setStyleSheet(matching_radio_style)

        # Add buttons to the group
        self.filter_button_group.addButton(self.hl_matching_btn)
        self.filter_button_group.addButton(self.weekday_matching_btn)

        # Connect radio buttons to handler
        self.hl_matching_btn.toggled.connect(self.on_matching_mode_changed)
        self.weekday_matching_btn.toggled.connect(self.on_matching_mode_changed)

        # Create a container for the radio buttons with reduced margin
        filter_container = QtWidgets.QWidget()
        filter_layout = QtWidgets.QVBoxLayout(filter_container)
        filter_layout.setContentsMargins(3, 5, 3, 5)  # Reduced margins
        filter_layout.setSpacing(5)  # Reduced spacing

        # Add buttons to the container
        filter_layout.addWidget(self.hl_matching_btn)
        filter_layout.addWidget(self.weekday_matching_btn)

        # Add the container to the right layout
        right_layout.addWidget(filter_container)

        # Add some spacing
        right_layout.addSpacing(8)

        # Add subtitle for Historical Data
        historical_subtitle = QtWidgets.QLabel("Historical Data")
        historical_subtitle.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 12px; padding: 3px; border-bottom: 1px solid {THEME_COLORS['borders']};")
        historical_subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(historical_subtitle)

        # Create the historical data dropdown
        self.historical_dropdown = QtWidgets.QComboBox()
        self.historical_dropdown.setFixedHeight(28)  # Slightly smaller
        self.historical_dropdown.addItem("Current Data")
        self.historical_dropdown.setStyleSheet(f"""
            QComboBox {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 3px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                min-height: 22px;
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 22px;
                border-left-width: 1px;
                border-left-color: {THEME_COLORS['borders']};
                border-left-style: solid;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }}
            QComboBox::down-arrow {{
                width: 12px;
                height: 12px;
            }}
            QComboBox QAbstractItemView {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                selection-color: {THEME_COLORS['text']};
            }}
        """)

        # Create a button to open the historical data dialog
        self.historical_button = QtWidgets.QPushButton("Select Historical Data")
        self.historical_button.setFixedHeight(32)  # Increased height to prevent text cutoff
        self.historical_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        self.historical_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """)
        self.historical_button.clicked.connect(self.show_historical_dialog)

        # Create a container for the historical data controls with vertical layout
        historical_container = QtWidgets.QWidget()
        historical_layout = QtWidgets.QVBoxLayout(historical_container)  # Changed to vertical layout
        historical_layout.setContentsMargins(3, 3, 3, 3)  # Reduced margins
        historical_layout.setSpacing(5)  # Reduced spacing

        # Create a horizontal layout for the dropdown and select button
        top_controls_layout = QtWidgets.QHBoxLayout()
        top_controls_layout.setContentsMargins(0, 0, 0, 0)
        top_controls_layout.setSpacing(5)  # Reduced spacing

        # Add the dropdown and select button to the top controls layout
        top_controls_layout.addWidget(self.historical_dropdown, 1)  # Give dropdown more space
        top_controls_layout.addWidget(self.historical_button, 1)  # Equal space for button

        # Add the top controls layout to the main historical layout
        historical_layout.addLayout(top_controls_layout)

        # Create a "Back to Current Data" button
        self.back_to_current_button = QtWidgets.QPushButton("Back to Current Data")
        self.back_to_current_button.setFixedHeight(28)  # Slightly smaller
        self.back_to_current_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        self.back_to_current_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 3px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['background']};
                border: 1px solid #000000;
            }}
            QPushButton:pressed {{
                background-color: #1a1a1a;
                border: 1px solid #000000;
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['borders']};
                border: 1px solid {THEME_COLORS['borders']};
            }}
        """)
        self.back_to_current_button.clicked.connect(self.reset_to_current_data)
        # Initially disable the button since we're not viewing historical data
        self.back_to_current_button.setEnabled(False)

        # Add the back to current button to the main historical layout
        historical_layout.addWidget(self.back_to_current_button)

        # Add the container to the right layout
        right_layout.addWidget(historical_container)

        # Connect historical dropdown signal
        self.historical_dropdown.currentIndexChanged.connect(self.on_historical_dropdown_changed)

        # Add a spacer to push everything to the top
        right_layout.addStretch(1)

        # Add stretch to the left layout to keep it empty but expandable
        left_layout.addStretch()

        # Add the panels to the split layout with equal stretch factors
        settings_split_layout.addWidget(left_panel, 1)  # Stretch factor of 1

        # Create an extremely minimal vertical separator (almost invisible)
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.Shape.VLine)
        separator.setFrameShadow(QtWidgets.QFrame.Shadow.Plain)
        separator.setFixedWidth(1)  # Keep at 1px width
        # Use a very light color for the separator to make it almost invisible
        separator_color = THEME_COLORS.get('background', '#1e1e1e')  # Use background color or default dark
        separator.setStyleSheet(f"color: {separator_color}; background-color: {separator_color}; border: none;")
        settings_split_layout.addWidget(separator, 0)  # No stretch for separator

        settings_split_layout.addWidget(right_panel, 1)  # Stretch factor of 1

        # Add the split layout to the settings content
        settings_content_layout.addLayout(settings_split_layout)

        # Add the collapsible settings content to the main layout
        non_scroll_layout.addWidget(self.settings_content)

        # Keep the panel empty as requested
        # We'll just create an empty dictionary to store any future settings controls
        self.settings_controls = {}

        # Create a visible separator at the bottom of the settings panel
        bottom_separator = QtWidgets.QFrame()
        bottom_separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        bottom_separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)  # Changed to Sunken for better appearance
        bottom_separator.setFixedHeight(2)  # Slightly thicker for visibility
        bottom_separator.setStyleSheet(f"background-color: {THEME_COLORS['borders']}; border: none;")
        non_scroll_layout.addWidget(bottom_separator)

        # Add a small spacer after the separator
        non_scroll_layout.addSpacing(3)

        # Add stretch to push content to the top
        non_scroll_layout.addStretch()

        # Create a scroll area for the statistics box
        self.stats_scroll_area = QtWidgets.QScrollArea()
        self.stats_scroll_area.setFixedWidth(700)  # Make it even wider to accommodate more columns
        self.stats_scroll_area.setWidgetResizable(True)
        self.stats_scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.stats_scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.stats_scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-top: none;
                margin-bottom: 10px;
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS['control_panel']};
                width: 12px;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #808080;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #909090;
            }}
            QScrollBar::handle:vertical:pressed {{
                background-color: #707070;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                border: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """)

        # Create the content widget for the scroll area
        self.stats_box = QtWidgets.QWidget()
        self.stats_box.setStyleSheet(f"background-color: {THEME_COLORS['background']}; border: none;")

        # Create a layout for the stats box
        stats_box_layout = QtWidgets.QVBoxLayout(self.stats_box)
        stats_box_layout.setContentsMargins(5, 5, 5, 15)  # Increased bottom margin to 15px

        # Set the stats box as the widget for the scroll area
        self.stats_scroll_area.setWidget(self.stats_box)

        # Create a horizontal layout for the title and clear button
        title_row_layout = QtWidgets.QHBoxLayout()
        title_row_layout.setContentsMargins(0, 0, 0, 0)
        title_row_layout.setSpacing(10)

        # Add "Volatility Statistics" title
        stats_title = QtWidgets.QLabel("Volatility Statistics")
        stats_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; padding-bottom: 5px; border: none;")
        stats_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)

        # We'll create a floating price box instead of a static label

        # Create a clear button
        clear_button = QtWidgets.QPushButton("Clear All")
        clear_button.setFixedWidth(80)
        clear_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 3px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
                color: {THEME_COLORS['text']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)
        # Connect the button to the clear_all_toggles method
        clear_button.clicked.connect(self.clear_all_toggles)

        # Add title to layout with stretch to push clear button to the right
        title_row_layout.addWidget(stats_title, 1)  # Add with stretch factor
        title_row_layout.addWidget(clear_button, 0)  # No stretch for button

        # Add the title row layout to the main layout
        stats_box_layout.addLayout(title_row_layout)

        # Create a widget for column headers
        column_headers = QtWidgets.QWidget()
        column_headers_layout = QtWidgets.QHBoxLayout(column_headers)
        column_headers_layout.setContentsMargins(0, 0, 0, 0)
        column_headers_layout.setSpacing(10)

        # Create column header labels
        stats_header = QtWidgets.QLabel("Statistics")
        count_header = QtWidgets.QLabel("Count")
        winrate_header = QtWidgets.QLabel("WinRate")

        # Style the header labels
        header_style = f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 12px;"
        stats_header.setStyleSheet(header_style)
        count_header.setStyleSheet(header_style)
        winrate_header.setStyleSheet(header_style)



        # Add headers to the layout
        column_headers_layout.addWidget(QtWidgets.QLabel(""), 0)  # Empty label for toggle column
        column_headers_layout.addWidget(stats_header, 3)  # Larger width for statistics
        column_headers_layout.addWidget(count_header, 1)
        column_headers_layout.addWidget(winrate_header, 1)

        # Add the column headers to the main layout
        stats_box_layout.addWidget(column_headers)

        # Create a container for statistics rows
        self.stats_container = QtWidgets.QWidget()
        stats_container_layout = QtWidgets.QVBoxLayout(self.stats_container)
        stats_container_layout.setContentsMargins(0, 0, 0, 0)
        stats_container_layout.setSpacing(2)

        # No basic statistics rows as per request

        # Create rows for apex statistics
        self.bear_apex_low_row = self.create_stat_row("Low $ at or above Apex", "--")
        self.bull_apex_high_row = self.create_stat_row("High $ at or below Apex", "--")

        # Create rows for high statistics
        self.bear_high_maxavg_row = self.create_stat_row("Low $ at or above MaxAvg", "--")
        self.bull_high_maxavg_row = self.create_stat_row("High $ at or below MaxAvg", "--")
        self.bear_high_avg_row = self.create_stat_row("Low $ at or above Avg", "--")
        self.bull_high_avg_row = self.create_stat_row("High $ at or below Avg", "--")
        self.bear_high_minavg_row = self.create_stat_row("Low $ at or above MinAvg", "--")
        self.bull_high_minavg_row = self.create_stat_row("High $ at or below MinAvg", "--")

        # Create rows for low statistics
        self.bear_low_maxavg_row = self.create_stat_row("Low $ at or above MaxAvg", "--")
        self.bull_low_maxavg_row = self.create_stat_row("High $ at or below MaxAvg", "--")
        self.bear_low_avg_row = self.create_stat_row("Low $ at or above Avg", "--")
        self.bull_low_avg_row = self.create_stat_row("High $ at or below Avg", "--")
        self.bear_low_minavg_row = self.create_stat_row("Low $ at or above MinAvg", "--")
        self.bull_low_minavg_row = self.create_stat_row("High $ at or below MinAvg", "--")

        # Create rows for median statistics
        self.bear_long_median_row = self.create_stat_row("Low $ at or above High Median", "--")
        self.bull_long_median_row = self.create_stat_row("High $ at or below High Median", "--")
        self.bear_short_median_row = self.create_stat_row("Low $ at or above Low Median", "--")
        self.bull_short_median_row = self.create_stat_row("High $ at or below Low Median", "--")

        # Create rows for wall statistics
        self.bear_wall_long_row = self.create_stat_row("Low $ at or above High Barrier", "--")
        self.bull_wall_long_row = self.create_stat_row("High $ at or below High Barrier", "--")
        self.bear_wall_short_row = self.create_stat_row("Low $ at or above Low Barrier", "--")
        self.bull_wall_short_row = self.create_stat_row("High $ at or below Low Barrier", "--")

        # Create rows for density statistics (ETH Options Zones)
        self.density_call_peak_row = self.create_stat_row("High $ at or below Peak Call Side ETH", "--")
        self.density_put_peak_row = self.create_stat_row("Low $ at or above Peak Put Side ETH", "--")
        self.density_call_inner_wall_row = self.create_stat_row("High $ at or below Inner Wall Call Side ETH", "--")
        self.density_put_inner_wall_row = self.create_stat_row("Low $ at or above Inner Wall Put Side ETH", "--")
        self.density_call_wall_row = self.create_stat_row("High $ at or below Wall Call Side ETH", "--")
        self.density_put_wall_row = self.create_stat_row("Low $ at or above Wall Put Side ETH", "--")
        self.density_call_overflow_row = self.create_stat_row("High $ at or below Overflow Call Side ETH", "--")
        self.density_put_overflow_row = self.create_stat_row("Low $ at or above Overflow Put Side ETH", "--")

        # No basic rows to add as per request

        # Add apex rows
        stats_container_layout.addWidget(self.bear_apex_low_row)
        stats_container_layout.addWidget(self.bull_apex_high_row)

        # Add high rows
        stats_container_layout.addWidget(self.bear_high_maxavg_row)
        stats_container_layout.addWidget(self.bull_high_maxavg_row)
        stats_container_layout.addWidget(self.bear_high_avg_row)
        stats_container_layout.addWidget(self.bull_high_avg_row)
        stats_container_layout.addWidget(self.bear_high_minavg_row)
        stats_container_layout.addWidget(self.bull_high_minavg_row)

        # Add low rows
        stats_container_layout.addWidget(self.bear_low_maxavg_row)
        stats_container_layout.addWidget(self.bull_low_maxavg_row)
        stats_container_layout.addWidget(self.bear_low_avg_row)
        stats_container_layout.addWidget(self.bull_low_avg_row)
        stats_container_layout.addWidget(self.bear_low_minavg_row)
        stats_container_layout.addWidget(self.bull_low_minavg_row)

        # Add median rows
        stats_container_layout.addWidget(self.bear_long_median_row)
        stats_container_layout.addWidget(self.bull_long_median_row)
        stats_container_layout.addWidget(self.bear_short_median_row)
        stats_container_layout.addWidget(self.bull_short_median_row)

        # Add wall rows
        stats_container_layout.addWidget(self.bear_wall_long_row)
        stats_container_layout.addWidget(self.bull_wall_long_row)
        stats_container_layout.addWidget(self.bear_wall_short_row)
        stats_container_layout.addWidget(self.bull_wall_short_row)

        # Add density statistics rows (ETH Options Zones)
        stats_container_layout.addWidget(self.density_call_peak_row)
        stats_container_layout.addWidget(self.density_put_peak_row)
        stats_container_layout.addWidget(self.density_call_inner_wall_row)
        stats_container_layout.addWidget(self.density_put_inner_wall_row)
        stats_container_layout.addWidget(self.density_call_wall_row)
        stats_container_layout.addWidget(self.density_put_wall_row)
        stats_container_layout.addWidget(self.density_call_overflow_row)
        stats_container_layout.addWidget(self.density_put_overflow_row)

        # Add the stats container to the main layout
        stats_box_layout.addWidget(self.stats_container)

        # Add a separator line for Density Statistics
        self.density_separator_line = QtWidgets.QFrame()
        self.density_separator_line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        self.density_separator_line.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        self.density_separator_line.setStyleSheet(f"background-color: {THEME_COLORS['borders']};")
        self.density_separator_line.setFixedHeight(2)
        stats_box_layout.addWidget(self.density_separator_line)

        # Add title
        density_stats_title = QtWidgets.QLabel("Projection Statistics")
        density_stats_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 14px; padding: 5px 0px; border: none;")
        density_stats_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        stats_box_layout.addWidget(density_stats_title)

        # Add a separator line for projected Prices
        self.separator_line = QtWidgets.QFrame()
        self.separator_line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        self.separator_line.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        self.separator_line.setStyleSheet(f"background-color: {THEME_COLORS['borders']};")
        self.separator_line.setFixedHeight(2)

        # Create a horizontal layout for the projected title and buttons
        projected_title_layout = QtWidgets.QHBoxLayout()
        projected_title_layout.setContentsMargins(0, 0, 0, 0)
        projected_title_layout.setSpacing(10)

        # Create labels for projected prices
        self.projected_title = QtWidgets.QLabel("Projected Prices")
        self.projected_title.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 12px; padding-top: 5px;")
        self.projected_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)

        # Create button style
        button_style = f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 3px;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
                color: {THEME_COLORS['text']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """

        # Create a "Select All projecteds" button
        select_all_projecteds_button = QtWidgets.QPushButton("Select All")
        select_all_projecteds_button.setFixedWidth(80)
        select_all_projecteds_button.setStyleSheet(button_style)
        # Connect the button to the select_all_projecteds method
        select_all_projecteds_button.clicked.connect(self.select_all_projecteds)

        # Create a "Select Long" button
        select_long_button = QtWidgets.QPushButton("Select Long")
        select_long_button.setFixedWidth(80)
        select_long_button.setStyleSheet(button_style)
        # Connect the button to the select_long_projecteds method
        select_long_button.clicked.connect(self.select_long_projecteds)

        # Create a "Select Short" button
        select_short_button = QtWidgets.QPushButton("Select Short")
        select_short_button.setFixedWidth(80)
        select_short_button.setStyleSheet(button_style)
        # Connect the button to the select_short_projecteds method
        select_short_button.clicked.connect(self.select_short_projecteds)

        # Add title to layout with stretch to push buttons to the right
        projected_title_layout.addWidget(self.projected_title, 1)  # Add with stretch factor
        projected_title_layout.addWidget(select_long_button, 0)  # No stretch for button
        projected_title_layout.addWidget(select_short_button, 0)  # No stretch for button
        projected_title_layout.addWidget(select_all_projecteds_button, 0)  # No stretch for button

        # Create a container for projected price labels
        self.projected_container = QtWidgets.QWidget()
        self.projected_layout = QtWidgets.QVBoxLayout(self.projected_container)
        self.projected_layout.setContentsMargins(0, 0, 0, 0)
        self.projected_layout.setSpacing(2)

        # Create labels for projected prices (will be populated dynamically)
        self.projected_labels = []

        # Add the separator line
        stats_box_layout.addWidget(self.separator_line)

        # Add the projected title layout
        stats_box_layout.addLayout(projected_title_layout)

        # Add the projected container
        stats_box_layout.addWidget(self.projected_container)

        # Add stretch at the bottom to push all labels to the top
        stats_box_layout.addStretch()

        # Create a vertical layout for the right side panels
        right_panel_layout = QtWidgets.QVBoxLayout()
        right_panel_layout.setContentsMargins(0, 0, 0, 10)  # Add bottom margin of 10px
        right_panel_layout.setSpacing(0)

        # Style the tab buttons widget for the chart tab
        self.tab_buttons_widget.setStyleSheet(f"""
            background-color: {THEME_COLORS['background']};
            border: 1px solid {THEME_COLORS['borders']};
            border-bottom: none;
        """)

        # Add a spacer at the top to align the side panel with the main chart border
        # This accounts for the height of the tab bar container and main tab widget tab bar
        # We'll calculate the proper height after the widgets are created
        self.top_spacer = QtWidgets.QSpacerItem(0, 0, QtWidgets.QSizePolicy.Policy.Minimum, QtWidgets.QSizePolicy.Policy.Fixed)
        right_panel_layout.addItem(self.top_spacer)

        # Add the non-scrollable panel and stats scroll area to the right panel layout
        right_panel_layout.addWidget(self.non_scroll_panel, 0)  # Add with stretch factor 0 to maintain fixed height
        right_panel_layout.addWidget(self.stats_scroll_area, 1)  # Add with stretch factor 1 to make it expand

        # Set spacing to 0 before adding widgets to ensure no gaps
        tab_layout.setSpacing(0)  # No spacing between elements

        # Ensure all margins are zero
        tab_layout.setContentsMargins(0, 0, 0, 0)

        # Add the tab bar container to the tab layout
        tab_layout.addWidget(self.tab_bar_container)

        # Create a tab widget for Chart and Data tabs
        self.main_tab_widget = QtWidgets.QTabWidget()
        # Disable tab closing buttons for consistency with main window
        self.main_tab_widget.setTabsClosable(False)
        self.main_tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS['borders']};
                background-color: {THEME_COLORS['background']};
                border-radius: 0px;
            }}
            QTabBar::tab {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                padding: 8px 16px;
                margin-right: -1px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                font-weight: bold;
                min-width: 140px;
                max-width: 140px;
                width: 140px;
                text-align: left;
            }}
            QTabBar::tab:selected {{
                background-color: {THEME_COLORS['background']};
                border-bottom-color: {THEME_COLORS['background']};
                padding: 8px 16px;
                min-width: 140px;
                max-width: 140px;
                width: 140px;
                text-align: left;
            }}
            QTabBar::tab:!selected {{
                margin-top: 2px;
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {THEME_COLORS['control_panel']};
            }}
        """)

        # Create a widget for the Chart tab content
        chart_tab = QtWidgets.QWidget()
        chart_tab_layout = QtWidgets.QVBoxLayout(chart_tab)
        chart_tab_layout.setContentsMargins(0, 0, 0, 10)  # Add bottom margin of 10px
        chart_tab_layout.setSpacing(0)  # No spacing between elements

        # Add the radio buttons at the top of the chart tab
        chart_tab_layout.addWidget(self.tab_buttons_widget, 0)  # Add with stretch factor 0 to maintain fixed height

        # Add the stacked widget to the Chart tab with maximum stretch to fill available space
        chart_tab_layout.addWidget(self.stacked_widget, 1)

        # Create a widget for the Data tab content
        data_tab = QtWidgets.QWidget()
        data_tab_layout = QtWidgets.QVBoxLayout(data_tab)
        data_tab_layout.setContentsMargins(10, 10, 10, 10)
        data_tab_layout.setSpacing(0)

        # Create a vertical layout to stack the Data tab sections
        data_split_layout = QtWidgets.QVBoxLayout()
        data_split_layout.setContentsMargins(0, 0, 0, 0)
        data_split_layout.setSpacing(10)

        # Create top section widget for Projected Highs
        top_section = QtWidgets.QWidget()
        top_section.setStyleSheet(f"background-color: {THEME_COLORS['background']}; border: none;")
        top_layout = QtWidgets.QVBoxLayout(top_section)
        top_layout.setContentsMargins(10, 10, 10, 10)
        top_layout.setSpacing(10)

        # Add title for Projected Highs
        projected_highs_title = QtWidgets.QLabel("Projected Highs")
        projected_highs_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        projected_highs_title.setStyleSheet(f"""
            color: {THEME_COLORS['text']};
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            padding: 5px;
        """)
        top_layout.addWidget(projected_highs_title)

        # Create table for Projected Highs
        self.projected_highs_table = QtWidgets.QTableWidget()
        self.projected_highs_table.setColumnCount(6)
        self.projected_highs_table.setHorizontalHeaderLabels(["Index", "$ Change High", "% Change High", "$ Proj. Change High", "% Proj. Change High", "Projected High"])
        self.projected_highs_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                gridline-color: {THEME_COLORS['borders']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                selection-color: {THEME_COLORS['text']};
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                padding: 5px;
                border: 1px solid {THEME_COLORS['borders']};
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: 5px;
                background-color: {THEME_COLORS['background']};
            }}
            QTableWidget::item:selected {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS['control_panel']};
                width: 12px;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #808080;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #909090;
            }}
            QScrollBar::handle:vertical:pressed {{
                background-color: #707070;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                border: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
            QScrollBar:horizontal {{
                background-color: {THEME_COLORS['control_panel']};
                height: 12px;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: #808080;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: #909090;
            }}
            QScrollBar::handle:horizontal:pressed {{
                background-color: #707070;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                border: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
        """)

        # Configure table properties
        self.projected_highs_table.setAlternatingRowColors(True)
        self.projected_highs_table.setSortingEnabled(True)
        self.projected_highs_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.projected_highs_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.projected_highs_table.verticalHeader().setVisible(False)
        # Make the entire table read-only
        self.projected_highs_table.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.NoEditTriggers)

        # Add table to layout
        top_layout.addWidget(self.projected_highs_table, 1)

        # Create bottom section widget for Projected Lows
        bottom_section = QtWidgets.QWidget()
        bottom_section.setStyleSheet(f"background-color: {THEME_COLORS['background']}; border: none;")
        bottom_layout = QtWidgets.QVBoxLayout(bottom_section)
        bottom_layout.setContentsMargins(10, 10, 10, 10)
        bottom_layout.setSpacing(10)

        # Add title for Projected Lows
        projected_lows_title = QtWidgets.QLabel("Projected Lows")
        projected_lows_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        projected_lows_title.setStyleSheet(f"""
            color: {THEME_COLORS['text']};
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            padding: 5px;
        """)
        bottom_layout.addWidget(projected_lows_title)

        # Create table for Projected Lows
        self.projected_lows_table = QtWidgets.QTableWidget()
        self.projected_lows_table.setColumnCount(6)
        self.projected_lows_table.setHorizontalHeaderLabels(["Index", "$ Change Low", "% Change Low", "$ Proj. Change Low", "% Proj. Change Low", "Projected Low"])
        self.projected_lows_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                gridline-color: {THEME_COLORS['borders']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                selection-color: {THEME_COLORS['text']};
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                padding: 5px;
                border: 1px solid {THEME_COLORS['borders']};
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: 5px;
                background-color: {THEME_COLORS['background']};
            }}
            QTableWidget::item:selected {{
                background-color: {THEME_COLORS['primary_accent']};
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS['control_panel']};
                width: 12px;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #808080;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #909090;
            }}
            QScrollBar::handle:vertical:pressed {{
                background-color: #707070;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                border: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
            QScrollBar:horizontal {{
                background-color: {THEME_COLORS['control_panel']};
                height: 12px;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: #808080;
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: #909090;
            }}
            QScrollBar::handle:horizontal:pressed {{
                background-color: #707070;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                border: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
        """)

        # Configure table properties
        self.projected_lows_table.setAlternatingRowColors(True)
        self.projected_lows_table.setSortingEnabled(True)
        self.projected_lows_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.projected_lows_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.projected_lows_table.verticalHeader().setVisible(False)
        # Make the entire table read-only
        self.projected_lows_table.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.NoEditTriggers)

        # Add table to layout
        bottom_layout.addWidget(self.projected_lows_table, 1)

        # Create horizontal separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator.setStyleSheet(f"background-color: {THEME_COLORS['borders']}; color: {THEME_COLORS['borders']};")

        # Add widgets to the split layout (stacked vertically)
        data_split_layout.addWidget(top_section, 1)  # Equal stretch for projected highs
        data_split_layout.addWidget(separator, 0)  # No stretch for separator
        data_split_layout.addWidget(bottom_section, 1)  # Equal stretch for projected lows

        # Add the split layout to the main data tab layout
        data_tab_layout.addLayout(data_split_layout)

        # Add the tabs to the tab widget
        self.main_tab_widget.addTab(chart_tab, "Chart")
        self.main_tab_widget.addTab(data_tab, "Data")

        # Connect the main tab widget's tab change event
        self.main_tab_widget.currentChanged.connect(self.on_main_tab_changed)

        # Add the main tab widget to the tab layout with no spacing
        tab_layout.addWidget(self.main_tab_widget)

        # Force the main tab widget to have no extra space
        self.main_tab_widget.setContentsMargins(0, 0, 0, 0)

        # Add the tab layout to the content layout
        content_layout.addLayout(tab_layout, 1)  # Add with stretch factor 1 to make it expand
        content_layout.addLayout(right_panel_layout, 0)  # Add with stretch factor 0 to maintain fixed width

        # Add the content layout to the main layout
        main_layout.addLayout(content_layout)

        # Connect to the stacked widget's resize event to adjust the stats box height
        self.stacked_widget.resizeEvent = self.on_tab_widget_resize

        # Set background color to match the app theme
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

        # Initialize settings panel state
        self.settings_expanded = True

        # We'll create the floating price box in each tab's plot widget when needed

        # Schedule initial side panel alignment after the UI is fully rendered
        QtCore.QTimer.singleShot(100, self.initial_side_panel_alignment)

    def get_data_tab(self):
        """Get the Data tab reference."""
        return self.data_tab

    def initial_side_panel_alignment(self):
        """Set initial side panel alignment after UI is fully rendered."""
        try:
            # Get the tab bar heights
            tab_bar_height = self.tab_bar_container.height() if hasattr(self, 'tab_bar_container') else 0
            main_tab_bar_height = self.main_tab_widget.tabBar().height() if hasattr(self, 'main_tab_widget') else 0

            # Update the side panel alignment
            self.update_side_panel_alignment(tab_bar_height, main_tab_bar_height)
        except Exception as e:
            logger.warning(f"Error setting initial side panel alignment: {str(e)}")

    def toggle_settings_panel(self, event):
        """Toggle the visibility of the settings panel content."""
        self.settings_expanded = not self.settings_expanded
        self.settings_content.setVisible(self.settings_expanded)

        # Update the arrow direction
        if self.settings_expanded:
            self.collapse_arrow.setText("▼")
        else:
            self.collapse_arrow.setText("▶")

        # Accept the event to prevent further processing
        event.accept()

    def wrap_refresh_data(self, original_refresh_data):
        """Wrap the data_tab's refresh_data method to update all substabs when data is fetched.

        Args:
            original_refresh_data: The original refresh_data method of the data_tab

        Returns:
            function: The wrapped refresh_data method
        """
        def wrapped_refresh_data(*args, **kwargs):
            # Call the original refresh_data method
            result = original_refresh_data(*args, **kwargs)

            # After data is fetched, update all substabs
            logger.info("Data fetched, updating all substabs")
            self.update_all_substabs()

            return result

        return wrapped_refresh_data

    def on_data_fetched_universal(self, symbol, data):
        """Handle data_fetched signal from universal_controls.

        Args:
            symbol (str): The symbol for which data was fetched
            data (pd.DataFrame): The fetched data
        """
        logger.info(f"Data fetched from universal_controls for {symbol}, updating all substabs")
        # Reset generation flags when new data is fetched
        self._reset_generation_flags()
        self.update_all_substabs()

    def on_data_fetched_market_odds(self, symbol, timeframe, days_to_load):
        """Handle data_fetched signal from market_odds_tab.

        Args:
            symbol (str): The symbol for which data was fetched
            timeframe (str): The timeframe of the fetched data
            days_to_load (int): The number of days loaded
        """
        logger.info(f"Data fetched from market_odds_tab for {symbol} with timeframe {timeframe}, updating all substabs")
        # Reset generation flags when new data is fetched
        self._reset_generation_flags()
        self.update_all_substabs()

    def _reset_generation_flags(self):
        """Reset all generation flags to force regeneration when new data is available."""
        self._has_been_shown = False
        self._tab_generated = {}
        self._data_tables_populated = False

    def on_parameter_changed(self, name, value):
        """Handle parameter changes from the parameter registry.

        Args:
            name (str): The name of the parameter that changed
            value: The new value of the parameter
        """
        # Check if the parameter is timeframe
        if name == 'timeframe':
            logger.info(f"Parameter '{name}' changed to {value}")

            # We no longer update all substabs here - only when data is fetched

        # We no longer sync vector_length with FWL Aggr. as they are separate parameters

    def update_all_substabs(self):
        """Update all substabs with the current settings."""
        logger.info("Updating all substabs directly")

        # Directly call the generation methods for each tab
        # This avoids the need to switch tabs which might be causing the issue

        # Update Volatility Graph tab
        if hasattr(self.volatility_graph_tab, 'generate_volatility_graph'):
            logger.info("Directly regenerating Volatility Graph tab")
            self.volatility_graph_tab.generate_volatility_graph()

        # Update Density Graph tab
        if hasattr(self.density_graph_tab, 'generate_density_graph'):
            logger.info("Directly regenerating Density Graph tab")
            self.density_graph_tab.generate_density_graph()

        # Update FWL Odds tab
        if hasattr(self.fwl_odds_tab, 'generate_fwl_odds'):
            logger.info("Directly regenerating FWL Odds tab")
            self.fwl_odds_tab.generate_fwl_odds()

    def on_tab_changed(self, index):
        """Handle subtab change event (Volatility Graph, Density Graph, FWL Odds).

        Args:
            index: Index of the selected subtab
        """
        # Only regenerate if the tab hasn't been generated yet or if data has changed
        # This prevents unnecessary regeneration when just switching between tabs
        if not hasattr(self, '_tab_generated') or not self._tab_generated.get(index, False):
            # Check if we need to regenerate the tab
            # Use a slightly longer delay for tab changes to ensure UI is fully updated
            if self.update_timer.isActive():
                self.update_timer.stop()

            # Use a longer delay for tab changes to ensure UI is fully rendered
            QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=False))

            # Mark this tab as generated
            if not hasattr(self, '_tab_generated'):
                self._tab_generated = {}
            self._tab_generated[index] = True

        # If we're on the Data tab, update the data tables (only if not already populated)
        if self.main_tab_widget.currentIndex() == 1:  # Data tab
            if not hasattr(self, '_data_tables_populated') or not self._data_tables_populated:
                QtCore.QTimer.singleShot(300, self.populate_data_tables)
                self._data_tables_populated = True

    def on_main_tab_changed(self, index):
        """Handle main tab change event (Chart, Data).

        Args:
            index: Index of the selected main tab
        """
        logger.info(f"Main tab changed to index {index}")

        # The radio buttons are now inside the Chart tab, so no need to show/hide them
        if index == 0:  # Chart tab
            # Only regenerate the current chart if it hasn't been generated yet
            current_subtab_index = self.stacked_widget.currentIndex()
            if not hasattr(self, '_tab_generated') or not self._tab_generated.get(current_subtab_index, False):
                if self.update_timer.isActive():
                    self.update_timer.stop()
                QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=False))

                # Mark this tab as generated
                if not hasattr(self, '_tab_generated'):
                    self._tab_generated = {}
                self._tab_generated[current_subtab_index] = True
        else:  # Data tab
            # Populate the data tables only if not already populated
            if not hasattr(self, '_data_tables_populated') or not self._data_tables_populated:
                QtCore.QTimer.singleShot(200, self.populate_data_tables)
                self._data_tables_populated = True

    def populate_data_tables(self):
        """Populate the Projected Highs and Projected Lows tables with data."""
        logger.info("Populating data tables")

        # Clear existing data
        self.projected_highs_table.setRowCount(0)
        self.projected_lows_table.setRowCount(0)

        # Get projected prices from the current tab
        projected_prices = self.get_projected_prices()

        if not projected_prices:
            logger.warning("No projected prices available")
            return

        # Separate high and low prices
        high_prices = [tp for tp in projected_prices if tp['type'] == 'high']
        low_prices = [tp for tp in projected_prices if tp['type'] == 'low']

        # Get the current price from the data tab
        current_price = self.get_current_price_from_data_tab()
        if current_price is None:
            logger.warning("Current price not available from data tab, falling back to default")
            current_price = self.get_current_price()  # Fallback to the original method
            if current_price is None:
                logger.warning("No current price available")
                return

        logger.info(f"Using current price: ${current_price:.2f}")

        # Define colors for values above/below current price
        green_color = QtGui.QColor(0, 150, 0)  # Green for values above current price
        red_color = QtGui.QColor(150, 0, 0)    # Red for values below current price

        # Populate Projected Highs table
        self.projected_highs_table.setRowCount(len(high_prices))
        for i, tp in enumerate(high_prices):
            # Get data from data_tab for this index
            data_tab_row = self.get_data_tab_row_by_index(tp.get('idx', 0))

            # Calculate $ change (projected high - current price)
            # Example: If projected high is $105 and current price is $100, $ change is $5
            dollar_change = tp['price'] - current_price

            # Calculate % change ((projected high - current price) / current price * 100)
            # Example: If projected high is $105 and current price is $100, % change is 5%
            percent_change = (dollar_change / current_price) * 100 if current_price != 0 else 0

            # Log the calculation for debugging
            logger.info(f"Projected High: ${tp['price']:.2f}, Current Price: ${current_price:.2f}, $ Change: ${dollar_change:.2f}, % Change: {percent_change:.2f}%")

            # Create Index item
            index_value = data_tab_row['index'] if data_tab_row else tp.get('idx', 0)
            index_item = QtWidgets.QTableWidgetItem(str(index_value))
            index_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create $ Change High item (from data_tab)
            data_dollar_change_high = data_tab_row['dollar_change_high'] if data_tab_row else 0.0
            data_dollar_change_item = QtWidgets.QTableWidgetItem(f"${data_dollar_change_high:.5f}")
            data_dollar_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create % Change High item (from data_tab)
            data_percent_change_high = data_tab_row['percent_change_high'] if data_tab_row else 0.0
            data_percent_change_item = QtWidgets.QTableWidgetItem(f"{data_percent_change_high:.2f}%")
            data_percent_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create $ Proj. Change High item
            proj_dollar_change_item = QtWidgets.QTableWidgetItem(f"${dollar_change:.2f}")
            proj_dollar_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create % Proj. Change High item
            proj_percent_change_item = QtWidgets.QTableWidgetItem(f"{percent_change:.2f}%")
            proj_percent_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create projected high item
            projected_high_item = QtWidgets.QTableWidgetItem(f"${tp['price']:.2f}")
            projected_high_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Set text color based on whether the projected high is above or below current price
            text_color = green_color if tp['price'] > current_price else red_color
            proj_dollar_change_item.setForeground(text_color)
            proj_percent_change_item.setForeground(text_color)
            projected_high_item.setForeground(text_color)

            # Make items read-only (not editable)
            index_item.setFlags(index_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            data_dollar_change_item.setFlags(data_dollar_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            data_percent_change_item.setFlags(data_percent_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            proj_dollar_change_item.setFlags(proj_dollar_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            proj_percent_change_item.setFlags(proj_percent_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            projected_high_item.setFlags(projected_high_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)

            # Add items to table
            self.projected_highs_table.setItem(i, 0, index_item)
            self.projected_highs_table.setItem(i, 1, data_dollar_change_item)
            self.projected_highs_table.setItem(i, 2, data_percent_change_item)
            self.projected_highs_table.setItem(i, 3, proj_dollar_change_item)
            self.projected_highs_table.setItem(i, 4, proj_percent_change_item)
            self.projected_highs_table.setItem(i, 5, projected_high_item)

        # Populate Projected Lows table
        self.projected_lows_table.setRowCount(len(low_prices))
        for i, tp in enumerate(low_prices):
            # Get data from data_tab for this index
            data_tab_row = self.get_data_tab_row_by_index(tp.get('idx', 0))

            # Calculate $ change (projected low - current price)
            # Example: If projected low is $95 and current price is $100, $ change is -$5
            dollar_change = tp['price'] - current_price

            # Calculate % change ((projected low - current price) / current price * 100)
            # Example: If projected low is $95 and current price is $100, % change is -5%
            percent_change = (dollar_change / current_price) * 100 if current_price != 0 else 0

            # Log the calculation for debugging
            logger.info(f"Projected Low: ${tp['price']:.2f}, Current Price: ${current_price:.2f}, $ Change: ${dollar_change:.2f}, % Change: {percent_change:.2f}%")

            # Create Index item
            index_value = data_tab_row['index'] if data_tab_row else tp.get('idx', 0)
            index_item = QtWidgets.QTableWidgetItem(str(index_value))
            index_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create $ Change Low item (from data_tab)
            data_dollar_change_low = data_tab_row['dollar_change_low'] if data_tab_row else 0.0
            data_dollar_change_item = QtWidgets.QTableWidgetItem(f"${data_dollar_change_low:.5f}")
            data_dollar_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create % Change Low item (from data_tab)
            data_percent_change_low = data_tab_row['percent_change_low'] if data_tab_row else 0.0
            data_percent_change_item = QtWidgets.QTableWidgetItem(f"{data_percent_change_low:.2f}%")
            data_percent_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create $ Proj. Change Low item
            proj_dollar_change_item = QtWidgets.QTableWidgetItem(f"${dollar_change:.2f}")
            proj_dollar_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create % Proj. Change Low item
            proj_percent_change_item = QtWidgets.QTableWidgetItem(f"{percent_change:.2f}%")
            proj_percent_change_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Create projected low item
            projected_low_item = QtWidgets.QTableWidgetItem(f"${tp['price']:.2f}")
            projected_low_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)

            # Set text color based on whether the projected low is above or below current price
            text_color = green_color if tp['price'] > current_price else red_color
            proj_dollar_change_item.setForeground(text_color)
            proj_percent_change_item.setForeground(text_color)
            projected_low_item.setForeground(text_color)

            # Make items read-only (not editable)
            index_item.setFlags(index_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            data_dollar_change_item.setFlags(data_dollar_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            data_percent_change_item.setFlags(data_percent_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            proj_dollar_change_item.setFlags(proj_dollar_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            proj_percent_change_item.setFlags(proj_percent_change_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)
            projected_low_item.setFlags(projected_low_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable)

            # Add items to table
            self.projected_lows_table.setItem(i, 0, index_item)
            self.projected_lows_table.setItem(i, 1, data_dollar_change_item)
            self.projected_lows_table.setItem(i, 2, data_percent_change_item)
            self.projected_lows_table.setItem(i, 3, proj_dollar_change_item)
            self.projected_lows_table.setItem(i, 4, proj_percent_change_item)
            self.projected_lows_table.setItem(i, 5, projected_low_item)

        logger.info(f"Populated tables with {len(high_prices)} high prices and {len(low_prices)} low prices")

    def get_projected_prices(self):
        """Get projected prices from the current tab.

        Returns:
            list: List of dictionaries with projected price information
        """
        # Get the current tab index
        current_index = self.stacked_widget.currentIndex()

        # Get projected prices based on the current tab
        if current_index == 0:  # Volatility Graph
            if hasattr(self.volatility_graph_tab, 'projected_prices'):
                return self.volatility_graph_tab.projected_prices
        elif current_index == 1:  # Density Graph
            if hasattr(self.density_graph_tab, 'projected_prices'):
                return self.density_graph_tab.projected_prices
        elif current_index == 2:  # FWL Odds
            if hasattr(self.fwl_odds_tab, 'projected_prices'):
                return self.fwl_odds_tab.projected_prices

        # If no projected prices found, try to get them from the stats data
        if hasattr(self, 'stats_data') and self.stats_data:
            # Convert stats data to projected prices format
            projected_prices = []
            for stat in self.stats_data:
                if 'price' in stat and 'type' in stat:
                    projected_prices.append({
                        'price': stat['price'],
                        'type': 'high' if stat['type'] == 'short' else 'low',
                        'idx': stat.get('idx', 0)
                    })
            return projected_prices

        return []

    def get_data_tab_row_by_index(self, idx):
        """Get data from data_tab by row index.

        Args:
            idx: The index of the row to retrieve

        Returns:
            dict: Dictionary with Index, $ Change High, % Change High, $ Change Low, % Change Low values, or None if not found
        """
        if not hasattr(self, 'data_tab') or self.data_tab is None:
            logger.warning("No data tab reference available")
            return None

        if not hasattr(self.data_tab, 'table_model') or self.data_tab.table_model is None:
            logger.warning("No table model available in data tab")
            return None

        # Get the data from the table model
        data = self.data_tab.table_model._data
        if data is None or data.empty:
            logger.warning("No data available in data tab table model")
            return None

        # Check if we're in current price mode
        if not hasattr(self.data_tab, 'calculation_mode') or self.data_tab.calculation_mode != "current_price":
            logger.warning("Data tab is not in current price mode")
            return None

        try:
            # Find the row with the matching index
            # The idx from projected_prices corresponds to the pandas DataFrame index
            if idx in data.index:
                row = data.loc[idx]

                # Extract the required values
                result = {
                    'index': idx,  # Use the actual index number, not the Time column
                    'dollar_change_high': row.get('$ Change High', 0.0),
                    'percent_change_high': row.get('% Change High', 0.0),
                    'dollar_change_low': row.get('$ Change Low', 0.0),
                    'percent_change_low': row.get('% Change Low', 0.0)
                }

                logger.info(f"Found data for index {idx}: {result}")
                return result
            else:
                logger.warning(f"Index {idx} not found in data tab")
                return None

        except Exception as e:
            logger.error(f"Error getting data tab row by index {idx}: {str(e)}", exc_info=True)
            return None

    def get_current_price_from_data_tab(self):
        """Get the current price specifically from the data tab.

        Returns:
            float: Current price from data tab or None if not available
        """
        # Try to get the current price from the data tab
        if hasattr(self, 'data_tab') and self.data_tab is not None:
            # First try to use the get_current_price method if it exists
            if hasattr(self.data_tab, 'get_current_price'):
                try:
                    current_price = self.data_tab.get_current_price()
                    if current_price is not None:
                        logger.info(f"Found current price from data_tab.get_current_price(): {current_price}")
                        return current_price
                except Exception as e:
                    logger.warning(f"Error getting current price from data_tab.get_current_price(): {str(e)}")

            # Check if the data tab has a calculation_mode attribute
            if hasattr(self.data_tab, 'calculation_mode'):
                # If the calculation mode is "current_price", we can use this data
                if self.data_tab.calculation_mode == "current_price":
                    # Try to get the current price from the market odds tab referenced by the data tab
                    if hasattr(self.data_tab, 'market_odds_tab') and self.data_tab.market_odds_tab is not None:
                        # Try to get the current price directly from the market_odds_tab
                        if hasattr(self.data_tab.market_odds_tab, 'current_price') and self.data_tab.market_odds_tab.current_price is not None:
                            logger.info(f"Found current price from data_tab.market_odds_tab.current_price: {self.data_tab.market_odds_tab.current_price}")
                            return self.data_tab.market_odds_tab.current_price

                        # Try to get the current price from the last close price in the data
                        if hasattr(self.data_tab.market_odds_tab, 'data') and self.data_tab.market_odds_tab.data is not None and not self.data_tab.market_odds_tab.data.empty:
                            current_price = self.data_tab.market_odds_tab.data['Close'].iloc[-1]
                            logger.info(f"Found current price from data_tab.market_odds_tab.data['Close'].iloc[-1]: {current_price}")
                            return current_price

        # If we couldn't get the current price from the data tab, return None
        logger.warning("Could not find current price from data tab")
        return None

    def get_current_price(self):
        """Get the current price.

        Returns:
            float: Current price or None if not available
        """
        # Try to get the current price from the market odds tab
        if hasattr(self, 'market_odds_tab') and self.market_odds_tab is not None:
            if hasattr(self.market_odds_tab, 'current_price') and self.market_odds_tab.current_price is not None:
                return self.market_odds_tab.current_price

        # Try to get the current price from the data tab
        if hasattr(self, 'data_tab') and self.data_tab is not None:
            if hasattr(self.data_tab, 'market_odds_tab') and self.data_tab.market_odds_tab is not None:
                if hasattr(self.data_tab.market_odds_tab, 'current_price') and self.data_tab.market_odds_tab.current_price is not None:
                    return self.data_tab.market_odds_tab.current_price

        # If no current price found, use a default value
        return 100.0  # Default value

    def showEvent(self, event):
        """Handle show event to generate the current tab when the tab is shown."""
        super().showEvent(event)
        # Only generate the current tab if it hasn't been generated yet (first time showing)
        # This prevents unnecessary refreshing when switching between tabs
        if not hasattr(self, '_has_been_shown') or not self._has_been_shown:
            self._has_been_shown = True
            # Generate the current tab when shown for the first time, but don't force regeneration
            # This will use cached data if available
            # Use a slightly longer delay for initial show to ensure UI is fully rendered
            if self.update_timer.isActive():
                self.update_timer.stop()

            # Use a longer delay for the initial show event to ensure UI is fully rendered
            QtCore.QTimer.singleShot(300, lambda: self.generate_current_tab(force=False))

    def on_tab_widget_resize(self, event):
        """Handle resize event for the stacked widget to adjust the stats box height.

        Args:
            event: The resize event
        """
        # Call the original resize event handler
        QtWidgets.QStackedWidget.resizeEvent(self.stacked_widget, event)

        # Get the parent widget's height (the main content area)
        parent_height = self.height()

        # Get the tab bar height
        tab_bar_height = self.tab_bar_container.height()

        # Get the main tab widget tab bar height
        main_tab_bar_height = self.main_tab_widget.tabBar().height()

        # Get the combined height of the right panel (settings + stats box)
        right_panel_height = self.non_scroll_panel.height() + self.stats_scroll_area.height()

        # Calculate the maximum possible height for the chart
        # We want it to extend to the maximum height possible
        # Account for both tab bars and add a margin to ensure the bottom is visible
        # Also account for the bottom margins we've added to various layouts (10px + 10px + 10px)
        max_possible_height = parent_height - tab_bar_height - main_tab_bar_height - 30  # Increased margin to 30px

        # Get the window height to ensure we don't exceed it
        window_height = self.window().height() if self.window() else parent_height

        # Make sure we don't exceed 95% of the window height
        max_possible_height = min(max_possible_height, window_height * 0.95)

        # Make the chart extend as far upward as possible
        # Use the maximum possible height to fill the available space
        chart_height = max_possible_height

        # Set the height of the stacked widget to the calculated height
        self.stacked_widget.setFixedHeight(chart_height)

        # Allow flexible height sizing for stats scroll area
        # Removed minimum height constraints to allow smaller window sizes

        # Allow flexible width sizing
        # Removed fixed width constraints to allow smaller window sizes

        # Update the top spacer height to align the side panel with the chart border
        self.update_side_panel_alignment(tab_bar_height, main_tab_bar_height)

        # Log the heights for debugging
        logger.info(f"Window height: {window_height}, Parent height: {parent_height}, Tab bar height: {tab_bar_height}, Main tab bar height: {main_tab_bar_height}, Right panel height: {right_panel_height}, Max possible height: {max_possible_height}, Chart height: {chart_height}")

        # Force a repaint to ensure the changes take effect
        self.stacked_widget.update()

    def update_side_panel_alignment(self, tab_bar_height, main_tab_bar_height):
        """Update the top spacer height to align the side panel with the main chart border.

        Args:
            tab_bar_height: Height of the tab bar container
            main_tab_bar_height: Height of the main tab widget tab bar
        """
        if hasattr(self, 'top_spacer'):
            # Calculate the total height of the tab bars that appear above the chart
            total_tab_height = tab_bar_height + main_tab_bar_height

            # Set the spacer height to match the tab bar heights
            # This will push the side panel down to align with the top of the chart border
            self.top_spacer.changeSize(0, total_tab_height, QtWidgets.QSizePolicy.Policy.Minimum, QtWidgets.QSizePolicy.Policy.Fixed)

            logger.info(f"Updated side panel alignment: spacer height set to {total_tab_height}px (tab_bar: {tab_bar_height}px + main_tab_bar: {main_tab_bar_height}px)")

    def clear_statistics_box(self):
        """Clear all statistics in the right box"""
        # No basic stat rows to reset as per request

        # Reset apex rows
        self.bear_apex_low_row.stat_label.setText("Low $ at or above Apex: --")
        self.bear_apex_low_row.count_label.setText("--")
        self.bear_apex_low_row.winrate_label.setText("--")

        self.bull_apex_high_row.stat_label.setText("High $ at or below Apex: --")
        self.bull_apex_high_row.count_label.setText("--")
        self.bull_apex_high_row.winrate_label.setText("--")

        # Reset high rows
        self.bear_high_maxavg_row.stat_label.setText("Low $ at or above MaxAvg: --")
        self.bear_high_maxavg_row.count_label.setText("--")
        self.bear_high_maxavg_row.winrate_label.setText("--")

        self.bull_high_maxavg_row.stat_label.setText("High $ at or below MaxAvg: --")
        self.bull_high_maxavg_row.count_label.setText("--")
        self.bull_high_maxavg_row.winrate_label.setText("--")

        self.bear_high_avg_row.stat_label.setText("Low $ at or above Avg: --")
        self.bear_high_avg_row.count_label.setText("--")
        self.bear_high_avg_row.winrate_label.setText("--")

        self.bull_high_avg_row.stat_label.setText("High $ at or below Avg: --")
        self.bull_high_avg_row.count_label.setText("--")
        self.bull_high_avg_row.winrate_label.setText("--")

        self.bear_high_minavg_row.stat_label.setText("Low $ at or above MinAvg: --")
        self.bear_high_minavg_row.count_label.setText("--")
        self.bear_high_minavg_row.winrate_label.setText("--")

        self.bull_high_minavg_row.stat_label.setText("High $ at or below MinAvg: --")
        self.bull_high_minavg_row.count_label.setText("--")
        self.bull_high_minavg_row.winrate_label.setText("--")

        # Reset low rows
        self.bear_low_maxavg_row.stat_label.setText("Low $ at or above MaxAvg: --")
        self.bear_low_maxavg_row.count_label.setText("--")
        self.bear_low_maxavg_row.winrate_label.setText("--")

        self.bull_low_maxavg_row.stat_label.setText("High $ at or below MaxAvg: --")
        self.bull_low_maxavg_row.count_label.setText("--")
        self.bull_low_maxavg_row.winrate_label.setText("--")

        self.bear_low_avg_row.stat_label.setText("Low $ at or above Avg: --")
        self.bear_low_avg_row.count_label.setText("--")
        self.bear_low_avg_row.winrate_label.setText("--")

        self.bull_low_avg_row.stat_label.setText("High $ at or below Avg: --")
        self.bull_low_avg_row.count_label.setText("--")
        self.bull_low_avg_row.winrate_label.setText("--")

        self.bear_low_minavg_row.stat_label.setText("Low $ at or above MinAvg: --")
        self.bear_low_minavg_row.count_label.setText("--")
        self.bear_low_minavg_row.winrate_label.setText("--")

        self.bull_low_minavg_row.stat_label.setText("High $ at or below MinAvg: --")
        self.bull_low_minavg_row.count_label.setText("--")
        self.bull_low_minavg_row.winrate_label.setText("--")

        # Reset median rows
        self.bear_long_median_row.stat_label.setText("Low $ at or above High Median: --")
        self.bear_long_median_row.count_label.setText("--")
        self.bear_long_median_row.winrate_label.setText("--")

        self.bull_long_median_row.stat_label.setText("High $ at or below High Median: --")
        self.bull_long_median_row.count_label.setText("--")
        self.bull_long_median_row.winrate_label.setText("--")

        self.bear_short_median_row.stat_label.setText("Low $ at or above Low Median: --")
        self.bear_short_median_row.count_label.setText("--")
        self.bear_short_median_row.winrate_label.setText("--")

        self.bull_short_median_row.stat_label.setText("High $ at or below Low Median: --")
        self.bull_short_median_row.count_label.setText("--")
        self.bull_short_median_row.winrate_label.setText("--")

        # Reset wall rows
        self.bear_wall_long_row.stat_label.setText("Low $ at or above High Barrier: --")
        self.bear_wall_long_row.count_label.setText("--")
        self.bear_wall_long_row.winrate_label.setText("--")

        self.bull_wall_long_row.stat_label.setText("High $ at or below High Barrier: --")
        self.bull_wall_long_row.count_label.setText("--")
        self.bull_wall_long_row.winrate_label.setText("--")

        self.bear_wall_short_row.stat_label.setText("Low $ at or above Low Barrier: --")
        self.bear_wall_short_row.count_label.setText("--")
        self.bear_wall_short_row.winrate_label.setText("--")

        self.bull_wall_short_row.stat_label.setText("High $ at or below Low Barrier: --")
        self.bull_wall_short_row.count_label.setText("--")
        self.bull_wall_short_row.winrate_label.setText("--")

        # Reset density statistics rows (ETH Options Zones)
        self.density_call_peak_row.stat_label.setText("High $ at or below Peak Call Side ETH: --")
        self.density_call_peak_row.count_label.setText("--")
        self.density_call_peak_row.winrate_label.setText("--")

        self.density_put_peak_row.stat_label.setText("Low $ at or above Peak Put Side ETH: --")
        self.density_put_peak_row.count_label.setText("--")
        self.density_put_peak_row.winrate_label.setText("--")

        self.density_call_inner_wall_row.stat_label.setText("High $ at or below Inner Wall Call Side ETH: --")
        self.density_call_inner_wall_row.count_label.setText("--")
        self.density_call_inner_wall_row.winrate_label.setText("--")

        self.density_put_inner_wall_row.stat_label.setText("Low $ at or above Inner Wall Put Side ETH: --")
        self.density_put_inner_wall_row.count_label.setText("--")
        self.density_put_inner_wall_row.winrate_label.setText("--")

        self.density_call_wall_row.stat_label.setText("High $ at or below Wall Call Side ETH: --")
        self.density_call_wall_row.count_label.setText("--")
        self.density_call_wall_row.winrate_label.setText("--")

        self.density_put_wall_row.stat_label.setText("Low $ at or above Wall Put Side ETH: --")
        self.density_put_wall_row.count_label.setText("--")
        self.density_put_wall_row.winrate_label.setText("--")

        self.density_call_overflow_row.stat_label.setText("High $ at or below Overflow Call Side ETH: --")
        self.density_call_overflow_row.count_label.setText("--")
        self.density_call_overflow_row.winrate_label.setText("--")

        self.density_put_overflow_row.stat_label.setText("Low $ at or above Overflow Put Side ETH: --")
        self.density_put_overflow_row.count_label.setText("--")
        self.density_put_overflow_row.winrate_label.setText("--")

        # Clear and remove any projected price labels
        for label in self.projected_labels:
            self.projected_layout.removeWidget(label)
            label.setParent(None)
            label.deleteLater()
        self.projected_labels = []

    def on_matching_mode_changed(self, checked):
        """Handle change in matching mode (H/L or Weekday)

        Args:
            checked (bool): Whether the button is checked
        """
        if not checked:
            # Skip if this is the button being unchecked
            return

        # Determine which mode is selected
        matching_mode = self.get_matching_mode()
        logger.info(f"Matching mode changed to: {matching_mode}")

        # Force regeneration of the current tab with the new matching mode
        # since the mode has changed, we need to regenerate regardless of cache
        # Use a slightly longer delay to ensure UI is updated first
        if self.update_timer.isActive():
            self.update_timer.stop()

        # Use a longer delay for mode changes to ensure UI is fully updated
        QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=True))

    def generate_current_tab(self, force=False):
        """Generate or regenerate the current tab with the current matching mode.

        Args:
            force (bool): If True, force regeneration even if cached data is available
        """
        # If the timer is active, stop it to prevent multiple updates
        if self.update_timer.isActive():
            self.update_timer.stop()

        # Start the timer to debounce multiple rapid update requests
        # This will call _delayed_generate_tab after the timeout
        self.update_timer.start(100)  # 100ms debounce time

        # Store force parameter for the delayed call
        self.update_timer.force = force

    def _delayed_generate_tab(self):
        """Actual implementation of tab generation, called after debounce timer expires"""
        # Get the force parameter from the timer
        force = getattr(self.update_timer, 'force', False)

        # Check if an update is already in progress to prevent multiple simultaneous updates
        if self.update_in_progress:
            logger.info("Update already in progress, skipping")
            return

        # Set the update flag
        self.update_in_progress = True

        try:
            # Get the current tab index
            current_index = self.stacked_widget.currentIndex()

            # Get the current matching mode
            matching_mode = self.get_matching_mode()

            # Create a cache key based on tab index and matching mode
            cache_key = f"{current_index}_{matching_mode}"

            # If viewing historical data, add the timestamp to the cache key
            if self.viewing_historical and self.historical_timestamp is not None:
                cache_key += f"_{self.historical_timestamp.strftime('%Y%m%d%H%M%S')}"

            # Check if we've updated this tab recently - skip this check if force=True
            # This allows us to update all tabs when timeframe or vector length changes
            if not force:
                current_time = time.time() * 1000  # Convert to milliseconds
                last_update = self.last_update_time.get(cache_key, 0)
                time_since_last_update = current_time - last_update

                # If it's been less than min_update_interval since the last update and not forced, skip
                if time_since_last_update < self.min_update_interval:
                    logger.info(f"Skipping update for tab {current_index}, last updated {time_since_last_update:.0f}ms ago")
                    self.update_in_progress = False
                    return

                # Check if we have cached data for this tab and mode
                cached_data = self.tab_data_cache.get(cache_key)
                if cached_data is not None:
                    logger.info(f"Using cached data for tab {current_index} with mode {matching_mode}")
                    # TODO: Apply cached data to the tab
                    # For now, we'll just skip regeneration since we don't have a way to apply cached data yet
                    self.update_in_progress = False
                    return

            # Always update the last update time when we're generating a tab
            current_time = time.time() * 1000  # Convert to milliseconds
            self.last_update_time[cache_key] = current_time

            # Update radio button text to indicate historical data if applicable
            if self.viewing_historical and self.historical_timestamp is not None:
                # Format the timestamp for display (shorter format for radio button text)
                timestamp_str = self.historical_timestamp.strftime("%m/%d %H:%M")

                # Update radio button text
                self.volatility_graph_btn.setText(f"Volatility Graph ({timestamp_str})")
                self.density_graph_btn.setText(f"Density Graph ({timestamp_str})")
                self.fwl_odds_btn.setText(f"FWL Odds ({timestamp_str})")
            else:
                # Reset radio button text to default
                self.volatility_graph_btn.setText("Volatility Graph")
                self.density_graph_btn.setText("Density Graph")
                self.fwl_odds_btn.setText("FWL Odds")

            # Regenerate the tab based on its type
            if current_index == 0:  # Volatility Graph
                if hasattr(self.volatility_graph_tab, 'generate_volatility_graph'):
                    logger.info("Regenerating Volatility Graph tab")
                    self.volatility_graph_tab.generate_volatility_graph()
            elif current_index == 1:  # Density Graph
                if hasattr(self.density_graph_tab, 'generate_density_graph'):
                    logger.info("Regenerating Density Graph tab")
                    self.density_graph_tab.generate_density_graph()
            elif current_index == 2:  # FWL Odds
                if hasattr(self.fwl_odds_tab, 'generate_fwl_odds'):
                    logger.info("Regenerating FWL Odds tab")
                    self.fwl_odds_tab.generate_fwl_odds()

            # TODO: Cache the generated data
            # self.tab_data_cache.put(cache_key, generated_data)

            logger.info(f"Regenerated tab at index {current_index} with mode {matching_mode}")

            # If we're on the Data tab, update the data tables
            if self.main_tab_widget.currentIndex() == 1:  # Data tab
                QtCore.QTimer.singleShot(300, self.populate_data_tables)
        finally:
            # Clear the update flag
            self.update_in_progress = False

    def get_matching_mode(self):
        """Get the currently selected matching mode

        Returns:
            str: 'hl' for H/L Matching or 'weekday' for Weekday Matching
        """
        if self.hl_matching_btn.isChecked():
            return 'hl'
        else:
            return 'weekday'

    def get_selected_expiry_date(self):
        """
        Get the currently selected expiry date from the density graph tab.

        Returns:
            str: Selected expiry date, or None if none selected
        """
        if hasattr(self, 'density_graph_tab') and self.density_graph_tab:
            return self.density_graph_tab.get_selected_expiry_date()
        return None

    def fetch_expiry_dates_for_ticker(self, ticker):
        """
        Fetch expiry dates for the given ticker in the density graph tab.

        Args:
            ticker: Stock symbol to fetch expiry dates for
        """
        if hasattr(self, 'density_graph_tab') and self.density_graph_tab:
            self.density_graph_tab.fetch_expiry_dates(ticker)

    def get_volatility_factor(self):
        """Get the current FWL Aggr. value

        Returns:
            int: The current FWL Aggr. value, or 1 if invalid
        """
        try:
            return int(self.numeric_input.text())
        except (ValueError, AttributeError):
            return 1

    def get_occurrence_count(self):
        """Get the current occurrence count value

        Returns:
            int: The current occurrence count value, or 0 if invalid (0 means all occurrences)
        """
        try:
            return int(self.occurrence_input.text())
        except (ValueError, AttributeError):
            return 0

    def increment_numeric_value(self):
        """Increment the numeric input value by 1, up to a maximum of 10"""
        try:
            current_value = int(self.numeric_input.text())
            # Increment by 1, but don't exceed 10
            new_value = min(current_value + 1, 10)
            self.numeric_input.setText(str(new_value))
            logger.info(f"Incremented FWL Aggr. to {new_value}")

            # Note: We don't need to manually update here since the textChanged signal
            # will trigger on_numeric_input_changed which will update the UI
        except ValueError:
            # If the current value is not a valid number, reset to 1
            self.numeric_input.setText("1")
            logger.warning("Invalid numeric value, reset to 1")

    def increment_occurrence_value(self):
        """Increment the occurrence count value by 1, up to a maximum of 100"""
        try:
            current_value = int(self.occurrence_input.text())
            # Increment by 1, but don't exceed 100
            new_value = min(current_value + 1, 100)
            self.occurrence_input.setText(str(new_value))
            logger.info(f"Incremented occurrence count to {new_value}")

            # Note: We no longer automatically update - user must press apply button
        except ValueError:
            # If the current value is not a valid number, reset to 0
            self.occurrence_input.setText("0")
            logger.warning("Invalid occurrence count value, reset to 0")

    def decrement_numeric_value(self):
        """Decrement the numeric input value by 1, down to a minimum of 1"""
        try:
            current_value = int(self.numeric_input.text())
            # Decrement by 1, but don't go below 1
            new_value = max(current_value - 1, 1)
            self.numeric_input.setText(str(new_value))
            logger.info(f"Decremented FWL Aggr. to {new_value}")

            # Note: We don't need to manually update here since the textChanged signal
            # will trigger on_numeric_input_changed which will update the UI
        except ValueError:
            # If the current value is not a valid number, reset to 1
            self.numeric_input.setText("1")
            logger.warning("Invalid numeric value, reset to 1")

    def decrement_occurrence_value(self):
        """Decrement the occurrence count value by 1, down to a minimum of 0"""
        try:
            current_value = int(self.occurrence_input.text())
            # Decrement by 1, but don't go below 0
            new_value = max(current_value - 1, 0)
            self.occurrence_input.setText(str(new_value))
            logger.info(f"Decremented occurrence count to {new_value}")

            # Note: We no longer automatically update - user must press apply button
        except ValueError:
            # If the current value is not a valid number, reset to 0
            self.occurrence_input.setText("0")
            logger.warning("Invalid occurrence count value, reset to 0")

    def apply_occurrence_limit(self):
        """Apply the occurrence limit when the Apply button is pressed"""
        try:
            # Get the current occurrence count
            occurrence_count = self.get_occurrence_count()
            logger.info(f"Applying occurrence limit: {occurrence_count}")

            # Force regeneration of the current tab with the new occurrence count
            if self.update_timer.isActive():
                self.update_timer.stop()

            # Use a slightly longer delay to ensure UI is updated first
            QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=True))

            # Show a status message (using dialog manager)
            from dialog_manager import information
            if occurrence_count > 0:
                information(
                    self,
                    "Occurrence Limit Applied",
                    f"Displaying the {occurrence_count} most recent occurrences. This overrides the 'days to load' setting."
                )
            else:
                information(
                    self,
                    "Occurrence Limit Applied",
                    "Displaying all available occurrences based on the 'days to load' setting."
                )
        except Exception as e:
            logger.error(f"Error applying occurrence limit: {str(e)}", exc_info=True)
            from dialog_manager import warning
            warning(
                self,
                "Error",
                f"Failed to apply occurrence limit: {str(e)}"
            )

    def reset_occurrence_limit(self):
        """Reset the occurrence limit to 0 (all occurrences)"""
        try:
            # Reset the occurrence input to 0
            self.occurrence_input.setText("0")
            logger.info("Reset occurrence limit to 0 (all occurrences)")

            # Force regeneration of the current tab with no occurrence limit
            if self.update_timer.isActive():
                self.update_timer.stop()

            # Use a slightly longer delay to ensure UI is updated first
            QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=True))

            # Show a status message (using dialog manager)
            from dialog_manager import information
            information(
                self,
                "Occurrence Limit Reset",
                "Displaying all available occurrences based on the 'days to load' setting."
            )
        except Exception as e:
            logger.error(f"Error resetting occurrence limit: {str(e)}", exc_info=True)
            from dialog_manager import warning
            warning(
                self,
                "Error",
                f"Failed to reset occurrence limit: {str(e)}"
            )

    def on_numeric_input_changed(self, text):
        """Handle changes to the numeric input field (FWL Aggr.)

        Args:
            text (str): The new text in the numeric input field
        """
        try:
            # Try to convert the text to an integer
            value = int(text)
            # Only update if the value is valid (between 1 and 10)
            if 1 <= value <= 10:
                logger.info(f"FWL Aggr. changed to {value}")

                # No longer update the vector_length parameter
                # FWL Aggregation and vector_length are separate parameters

                # Force regeneration of the current tab with the new FWL Aggr. value
                if self.update_timer.isActive():
                    self.update_timer.stop()

                # Use a slightly longer delay to ensure UI is updated first
                QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=True))

                # Also refresh the data tab to update with the new FWL Aggr. value
                if hasattr(self, 'data_tab') and self.data_tab is not None:
                    logger.info(f"Refreshing data tab with new FWL Aggr. value: {value}")
                    QtCore.QTimer.singleShot(300, self.data_tab.refresh_data)
        except ValueError:
            # Ignore invalid values
            pass

    def on_occurrence_input_changed(self, text):
        """Handle changes to the occurrence count input field

        Note: This method is no longer connected to textChanged signal.
        The occurrence limit is only applied when the Apply button is pressed.

        Args:
            text (str): The new text in the occurrence count input field
        """
        try:
            # Try to convert the text to an integer for validation
            value = int(text)
            # Only log if the value is valid (between 0 and 100)
            if 0 <= value <= 100:
                logger.info(f"Occurrence count input changed to {value} (not applied yet)")
        except ValueError:
            # Ignore invalid values
            pass

    def fetch_occurrences(self):
        """Handle the Fetch Occurrences button click"""
        try:
            # Get the current occurrence count
            occurrence_count = self.get_occurrence_count()
            logger.info(f"Fetching data with occurrence count: {occurrence_count}")

            # Force regeneration of the current tab with the new occurrence count
            if self.update_timer.isActive():
                self.update_timer.stop()

            # Use a slightly longer delay to ensure UI is updated first
            QtCore.QTimer.singleShot(200, lambda: self.generate_current_tab(force=True))

            # Show a status message (using dialog manager)
            from dialog_manager import information
            if occurrence_count > 0:
                information(
                    self,
                    "Occurrences Updated",
                    f"Displaying the {occurrence_count} most recent occurrences. This overrides the 'days to load' setting."
                )
            else:
                information(
                    self,
                    "Occurrences Updated",
                    "Displaying all available occurrences based on the 'days to load' setting."
                )
        except Exception as e:
            logger.error(f"Error fetching occurrences: {str(e)}", exc_info=True)
            from dialog_manager import warning
            warning(
                self,
                "Error",
                f"Failed to fetch occurrences: {str(e)}"
            )

    def on_stat_toggle_changed(self, checked):
        """Handle toggle change for a statistic

        Args:
            checked (bool): Whether the toggle is checked
        """
        # Get the sender (the checkbox that was toggled)
        checkbox = self.sender()
        if checkbox is None:
            logger.warning("Could not determine which statistic was toggled")
            return

        # Get the stat_id from the checkbox
        stat_id = checkbox.property("stat_id")
        if not stat_id:
            logger.warning("No stat_id found for toggled checkbox")
            return

        # Update the toggled_stats dictionary
        self.toggled_stats[stat_id] = checked
        logger.info(f"Toggled statistic '{stat_id}' to {checked}")

        # Regenerate the current tab to show/hide the toggled statistic
        self.generate_current_tab(force=True)

    def clear_all_toggles(self):
        """Clear all toggled statistics"""
        logger.info("Clearing all toggled statistics")

        # Clear the toggled_stats dictionary
        self.toggled_stats.clear()

        # Uncheck all toggle checkboxes
        # First, uncheck all bear and bull stat rows
        for stat in self.stats_data:
            if 'stat_id' in stat:
                # Find the corresponding row widget
                for row in self.stats_container.findChildren(QtWidgets.QWidget):
                    if hasattr(row, 'stat_id') and row.stat_id == stat['stat_id'] and hasattr(row, 'toggle_checkbox'):
                        # Uncheck the toggle checkbox without triggering the signal
                        row.toggle_checkbox.blockSignals(True)
                        row.toggle_checkbox.setChecked(False)
                        row.toggle_checkbox.blockSignals(False)

        # Then uncheck all projected price rows
        for row in self.projected_labels:
            if hasattr(row, 'toggle_checkbox'):
                # Uncheck the toggle checkbox without triggering the signal
                row.toggle_checkbox.blockSignals(True)
                row.toggle_checkbox.setChecked(False)
                row.toggle_checkbox.blockSignals(False)

        # Regenerate the current tab to update the display
        self.generate_current_tab(force=True)

    def select_all_projecteds(self):
        """Select all projected price toggles"""
        logger.info("Selecting all projected price toggles")

        # Count how many projected prices were selected
        count = 0

        # Check all projected price rows
        for row in self.projected_labels:
            if hasattr(row, 'toggle_checkbox') and hasattr(row, 'stat_id'):
                # Check if the stat_id starts with 'projected'
                if row.stat_id.startswith('projected'):
                    # Check the toggle checkbox without triggering the signal
                    row.toggle_checkbox.blockSignals(True)
                    row.toggle_checkbox.setChecked(True)
                    row.toggle_checkbox.blockSignals(False)

                    # Update the toggled_stats dictionary
                    self.toggled_stats[row.stat_id] = True
                    count += 1

        logger.info(f"Selected {count} projected price toggles")

        # Regenerate the current tab to update the display
        self.generate_current_tab(force=True)

    def select_long_projecteds(self):
        """Select all long projected price toggles"""
        logger.info("Selecting all long projected price toggles")

        # Count how many long projected prices were selected
        count = 0

        # Check all projected price rows
        for row in self.projected_labels:
            if hasattr(row, 'toggle_checkbox') and hasattr(row, 'stat_id'):
                # Check if the stat_id starts with 'projected_long'
                if row.stat_id.startswith('projected_long'):
                    # Check the toggle checkbox without triggering the signal
                    row.toggle_checkbox.blockSignals(True)
                    row.toggle_checkbox.setChecked(True)
                    row.toggle_checkbox.blockSignals(False)

                    # Update the toggled_stats dictionary
                    self.toggled_stats[row.stat_id] = True
                    count += 1

        logger.info(f"Selected {count} long projected price toggles")

        # Regenerate the current tab to update the display
        self.generate_current_tab(force=True)

    def select_short_projecteds(self):
        """Select all short projected price toggles"""
        logger.info("Selecting all short projected price toggles")

        # Count how many short projected prices were selected
        count = 0

        # Check all projected price rows
        for row in self.projected_labels:
            if hasattr(row, 'toggle_checkbox') and hasattr(row, 'stat_id'):
                # Check if the stat_id starts with 'projected_short'
                if row.stat_id.startswith('projected_short'):
                    # Check the toggle checkbox without triggering the signal
                    row.toggle_checkbox.blockSignals(True)
                    row.toggle_checkbox.setChecked(True)
                    row.toggle_checkbox.blockSignals(False)

                    # Update the toggled_stats dictionary
                    self.toggled_stats[row.stat_id] = True
                    count += 1

        logger.info(f"Selected {count} short projected price toggles")

        # Regenerate the current tab to update the display
        self.generate_current_tab(force=True)

    def get_toggled_stats(self):
        """Get the dictionary of toggled statistics

        Returns:
            dict: Dictionary with stat_id as key and boolean toggle state as value
        """
        return self.toggled_stats

    def get_stats_data(self):
        """Get the statistics data for plotting

        Returns:
            list: List of dictionaries with statistics data
        """
        return self.stats_data

    def get_historical_day_range(self):
        """Get the high and low prices of the day AFTER the selected historical day

        Returns:
            tuple: (high, low) prices of the next day after the historical day, or (None, None) if not viewing historical data or no next day available
        """
        if self.viewing_historical and self.historical_day_high is not None and self.historical_day_low is not None:
            return (self.historical_day_high, self.historical_day_low)
        return (None, None)

    def is_viewing_historical_data(self):
        """Check if currently viewing historical data

        Returns:
            bool: True if viewing historical data, False otherwise
        """
        return self.viewing_historical

    def get_toggled_stats_data(self):
        """Get the statistics data for toggled statistics

        Returns:
            list: List of dictionaries with statistics data for toggled statistics
        """
        # Filter the stats data to only include toggled statistics
        toggled_stats_data = []
        for stat in self.stats_data:
            if 'stat_id' in stat and stat['stat_id'] in self.toggled_stats and self.toggled_stats[stat['stat_id']]:
                toggled_stats_data.append(stat)
        return toggled_stats_data



    def reset_to_current_data(self):
        """Reset to current data"""
        try:
            # Check if we're viewing historical data
            if not self.viewing_historical or self.original_data is None:
                logger.info("Not viewing historical data or no original data available")
                return

            # Get the market odds tab from the data tab
            if self.data_tab is None or not hasattr(self.data_tab, 'market_odds_tab'):
                logger.error("No data tab or market odds tab reference available")
                QtWidgets.QMessageBox.warning(self, "Error", "Could not access market data.")
                return

            market_odds_tab = self.data_tab.market_odds_tab

            # Restore the original data
            market_odds_tab.data = self.original_data

            # Restore the original rebased_data if it was stored
            if hasattr(self, 'original_rebased_data') and self.original_rebased_data is not None:
                market_odds_tab.rebased_data = self.original_rebased_data
                logger.info("Restored original rebased_data")

            # Restore the original categories if they were stored
            if hasattr(self, 'original_categories') and self.original_categories is not None:
                market_odds_tab.percentage_based_categories = self.original_categories
                logger.info("Restored original categories")

            # Update the data tab
            if hasattr(self.data_tab, 'refresh_data'):
                self.data_tab.refresh_data()

            # Remove the historical cutoff index from market_odds_tab
            if hasattr(market_odds_tab, 'historical_cutoff_index'):
                delattr(market_odds_tab, 'historical_cutoff_index')
                logger.info("Removed historical cutoff index from market_odds_tab")

            # Reset the viewing historical flag and clear stored data
            self.viewing_historical = False
            self.original_data = None
            self.historical_timestamp = None
            self.historical_index = None

            # Clear the stored original rebased_data and categories
            if hasattr(self, 'original_rebased_data'):
                self.original_rebased_data = None
            if hasattr(self, 'original_categories'):
                self.original_categories = None

            # Clear the historical day high/low values
            self.historical_day_high = None
            self.historical_day_low = None

            # Disable the "Back to Current" button
            self.back_to_current_button.setEnabled(False)

            # Reset the dropdown to "Current Data" without triggering the signal
            self.historical_dropdown.blockSignals(True)
            self.historical_dropdown.setCurrentIndex(0)
            self.historical_dropdown.blockSignals(False)

            # Force regeneration of all tabs to ensure charts are updated
            self.update_all_substabs()

            # Also force regeneration of the current tab
            self.generate_current_tab(force=True)

            logger.info("Successfully reset to current data")

        except Exception as e:
            logger.error(f"Error resetting to current data: {str(e)}", exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", f"Error resetting to current data: {str(e)}")

    def show_historical_dialog(self):
        """Show a dialog to select historical data by date and time"""
        # Create a dialog
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Select Historical Data")
        dialog.setMinimumWidth(400)
        dialog.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

        # Create a layout for the dialog
        dialog_layout = QtWidgets.QVBoxLayout(dialog)
        dialog_layout.setContentsMargins(20, 20, 20, 20)
        dialog_layout.setSpacing(15)

        # Add a title
        title_label = QtWidgets.QLabel("Select Historical Date and Time")
        title_label.setStyleSheet(f"""
            color: {THEME_COLORS['text']};
            font-family: 'Consolas', 'Courier New', monospace;
            font-weight: bold;
            font-size: 14px;
        """)
        title_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        dialog_layout.addWidget(title_label)

        # Create a form layout for the date and time pickers
        form_layout = QtWidgets.QFormLayout()
        form_layout.setContentsMargins(10, 10, 10, 10)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        form_layout.setFieldGrowthPolicy(QtWidgets.QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # Create date picker
        date_label = QtWidgets.QLabel("Date:")
        date_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace;")

        date_picker = QtWidgets.QDateEdit()
        date_picker.setCalendarPopup(True)
        date_picker.setDate(QtCore.QDate.currentDate())
        date_picker.setDisplayFormat("MM/dd/yyyy")

        date_picker.setStyleSheet(f"""
            QDateEdit {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
            }}
            QDateEdit::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left-width: 1px;
                border-left-color: {THEME_COLORS['borders']};
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }}
            QDateEdit::down-arrow {{
                width: 14px;
                height: 14px;
            }}
        """)

        # Create time picker
        time_label = QtWidgets.QLabel("Time:")
        time_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace;")

        time_picker = QtWidgets.QTimeEdit()
        time_picker.setTime(QtCore.QTime.currentTime())
        time_picker.setDisplayFormat("hh:mm:ss AP")
        time_picker.setMinimumWidth(150)
        time_picker.setStyleSheet(f"""
            QTimeEdit {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
            }}
            QTimeEdit::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left-width: 1px;
                border-left-color: {THEME_COLORS['borders']};
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }}
            QTimeEdit::down-arrow {{
                width: 14px;
                height: 14px;
            }}
        """)

        # Add the widgets to the form layout
        form_layout.addRow(date_label, date_picker)
        form_layout.addRow(time_label, time_picker)

        # Add the form layout to the dialog layout
        dialog_layout.addLayout(form_layout)

        # Create a horizontal layout for the buttons
        button_layout = QtWidgets.QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)

        # Create the buttons
        load_button = QtWidgets.QPushButton("Load Historical Data")
        load_button.setMinimumHeight(35)
        load_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        load_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 8px 15px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """)

        cancel_button = QtWidgets.QPushButton("Cancel")
        cancel_button.setMinimumHeight(35)
        cancel_button.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 8px 15px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
                border: 1px solid {THEME_COLORS['highlight']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
            }}
        """)

        # Add the buttons to the button layout
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(load_button)

        # Add the button layout to the dialog layout
        dialog_layout.addLayout(button_layout)

        # Connect the buttons
        load_button.clicked.connect(lambda: self._load_historical_from_dialog(dialog, date_picker, time_picker))
        cancel_button.clicked.connect(dialog.reject)

        # Show the dialog
        dialog.exec()

    def _load_historical_from_dialog(self, dialog, date_picker, time_picker):
        """Load historical data from the dialog

        Args:
            dialog (QDialog): The dialog
            date_picker (QDateEdit): The date picker
            time_picker (QTimeEdit): The time picker
        """
        # Get the selected date and time
        selected_date = date_picker.date().toPyDate()
        selected_time = time_picker.time().toPyTime()

        # Combine into a datetime object
        selected_datetime = datetime.datetime.combine(selected_date, selected_time)

        # Load the historical data
        self._load_historical_data(selected_datetime)

        # Close the dialog
        dialog.accept()

    def on_historical_dropdown_changed(self, index):
        """Handle changes to the historical data dropdown

        Args:
            index (int): The selected index
        """
        # If the index is 0, reset to current data
        if index == 0:
            self.reset_to_current_data()
            return

        # Otherwise, load the historical data for the selected timestamp
        timestamp_str = self.historical_dropdown.itemText(index)
        try:
            # Parse the timestamp string (format: "YYYY-MM-DD HH:MM:SS")
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

            # Load the historical data and update all charts
            self._load_historical_data(timestamp)
        except Exception as e:
            logger.error(f"Error parsing timestamp: {str(e)}")
            QtWidgets.QMessageBox.warning(self, "Error", f"Could not parse timestamp: {str(e)}")

    def _load_historical_data(self, timestamp):
        """Load historical data for a specific timestamp

        Args:
            timestamp (datetime): The timestamp to load data for
        """
        try:
            # Log the selected datetime
            logger.info(f"Loading historical data for: {timestamp}")

            # Get the market odds tab from the data tab
            if self.data_tab is None or not hasattr(self.data_tab, 'market_odds_tab'):
                logger.error("No data tab or market odds tab reference available")
                QtWidgets.QMessageBox.warning(self, "Error", "Could not access market data. Please make sure data is loaded in the Market Odds tab.")
                return

            market_odds_tab = self.data_tab.market_odds_tab

            # Check if we have data
            if not hasattr(market_odds_tab, 'data') or market_odds_tab.data is None or market_odds_tab.data.empty:
                logger.error("No data available in market odds tab")
                QtWidgets.QMessageBox.warning(self, "Error", "No data available. Please load data in the Market Odds tab first.")
                return

            # If we're already viewing historical data, restore the original data first
            if self.viewing_historical and self.original_data is not None:
                logger.info("Already viewing historical data, restoring original data before applying new historical filter")
                # Restore original data
                market_odds_tab.data = self.original_data.copy()
                # Restore original rebased_data if it exists
                if hasattr(self, 'original_rebased_data') and self.original_rebased_data is not None:
                    market_odds_tab.rebased_data = self.original_rebased_data.copy()
                # Restore original categories if they exist
                if hasattr(self, 'original_categories') and self.original_categories is not None:
                    market_odds_tab.percentage_based_categories = self.original_categories.copy()
                # Remove any existing historical cutoff index
                if hasattr(market_odds_tab, 'historical_cutoff_index'):
                    delattr(market_odds_tab, 'historical_cutoff_index')

            # Get the original data (now restored if we were viewing historical)
            original_data = market_odds_tab.data

            # Store the original data if we haven't already (first time viewing historical)
            if not self.viewing_historical:
                self.original_data = original_data.copy()
                # Also store original rebased_data if it exists
                if hasattr(market_odds_tab, 'rebased_data'):
                    self.original_rebased_data = market_odds_tab.rebased_data.copy() if market_odds_tab.rebased_data else None
                # Store original categories if they exist
                if hasattr(market_odds_tab, 'percentage_based_categories'):
                    self.original_categories = market_odds_tab.percentage_based_categories.copy() if market_odds_tab.percentage_based_categories else None

            # Find the closest timestamp to the selected datetime
            timestamps = original_data.index

            # Convert timestamps to datetime if they're not already
            if not isinstance(timestamps[0], datetime.datetime):
                try:
                    timestamps = pd.to_datetime(timestamps)
                except Exception as e:
                    logger.error(f"Error converting timestamps to datetime: {str(e)}")
                    QtWidgets.QMessageBox.warning(self, "Error", "Could not process timestamps in the data.")
                    return

            # Find the closest timestamp
            closest_idx = None
            min_diff = None

            for i, ts in enumerate(timestamps):
                diff = abs((ts - timestamp).total_seconds())
                if min_diff is None or diff < min_diff:
                    min_diff = diff
                    closest_idx = i

            if closest_idx is None:
                logger.error("Could not find a matching timestamp")
                QtWidgets.QMessageBox.warning(self, "Error", "Could not find a matching timestamp in the data.")
                return

            # Get the actual timestamp that was found
            actual_timestamp = timestamps[closest_idx]

            # Calculate how far off we were
            time_diff = abs((actual_timestamp - timestamp).total_seconds())
            time_diff_str = self.format_time_difference(time_diff)

            # Log the found timestamp
            logger.info(f"Found closest timestamp: {actual_timestamp} (difference: {time_diff_str})")

            # Store the historical timestamp and index
            self.historical_timestamp = actual_timestamp
            self.historical_index = closest_idx

            # IMPORTANT: Keep ALL original data loaded, but mark the historical cutoff point
            # This ensures we keep all 200 rows but only use data up to the selected historical day for calculations
            # The charts will need to respect this cutoff when doing analysis

            # Store the historical cutoff index in the market_odds_tab for charts to use
            market_odds_tab.historical_cutoff_index = closest_idx
            logger.info(f"Set historical cutoff at index {closest_idx} (keeping all {len(original_data)} rows)")

            # Filter rebased_data to only include entries up to the historical timestamp index
            # Always use the original rebased_data as the source for filtering
            if hasattr(market_odds_tab, 'rebased_data') and market_odds_tab.rebased_data:
                # Create filtered rebased_data for historical analysis using original data
                historical_rebased_data = []
                source_rebased_data = self.original_rebased_data if self.original_rebased_data else market_odds_tab.rebased_data
                for entry in source_rebased_data:
                    # entry[0] is the index, check if it's within our historical range
                    if entry[0] <= closest_idx:
                        historical_rebased_data.append(entry)
                market_odds_tab.rebased_data = historical_rebased_data
                logger.info(f"Filtered rebased_data to {len(historical_rebased_data)} entries for historical analysis (from {len(source_rebased_data)} total)")

            # Filter categories to match the historical cutoff
            # Always use the original categories as the source for filtering
            if hasattr(market_odds_tab, 'percentage_based_categories') and market_odds_tab.percentage_based_categories:
                # Create filtered categories for historical analysis using original data
                historical_categories = {}
                source_categories = self.original_categories if self.original_categories else market_odds_tab.percentage_based_categories
                for idx, category in source_categories.items():
                    if idx <= closest_idx:
                        historical_categories[idx] = category
                market_odds_tab.percentage_based_categories = historical_categories
                logger.info(f"Filtered categories to {len(historical_categories)} entries for historical analysis (from {len(source_categories)} total)")

            # Keep the original data intact - DO NOT slice it
            # The data tab and charts will use the historical_cutoff_index to limit their analysis
            logger.info(f"Keeping all {len(original_data)} rows of data intact for historical analysis")

            # Log the reference price and category that will be used
            historical_close_price = original_data['Close'].iloc[closest_idx]
            logger.info(f"Historical reference price (close of selected day): {historical_close_price}")

            # Store the high and low prices of the NEXT day after the selected historical day for chart display
            # Check if there's a next day available in the original data
            if closest_idx + 1 < len(original_data):
                next_day_data = original_data.iloc[closest_idx + 1]
                self.historical_day_high = next_day_data['High']
                self.historical_day_low = next_day_data['Low']
                next_day_timestamp = original_data.index[closest_idx + 1]
                logger.info(f"Next day ({next_day_timestamp}) high: {self.historical_day_high}, low: {self.historical_day_low}")
            else:
                # If there's no next day available, clear the values
                self.historical_day_high = None
                self.historical_day_low = None
                logger.info("No next day available after the selected historical day")

            # Get the category for the selected historical day
            historical_category = None
            if hasattr(market_odds_tab, 'percentage_based_categories') and closest_idx in market_odds_tab.percentage_based_categories:
                historical_category = market_odds_tab.percentage_based_categories[closest_idx]
                logger.info(f"Historical category for selected day: {historical_category}")

            # Update the data tab
            if hasattr(self.data_tab, 'refresh_data'):
                self.data_tab.refresh_data()

            # Set the viewing historical flag
            self.viewing_historical = True

            # Enable the "Back to Current" button
            self.back_to_current_button.setEnabled(True)

            # Format the timestamp for display
            timestamp_str = actual_timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Add the timestamp to the dropdown if it's not already there
            found = False
            for i in range(self.historical_dropdown.count()):
                if self.historical_dropdown.itemText(i) == timestamp_str:
                    found = True
                    self.historical_dropdown.setCurrentIndex(i)
                    break

            if not found:
                self.historical_dropdown.addItem(timestamp_str)
                self.historical_dropdown.setCurrentIndex(self.historical_dropdown.count() - 1)

            # Force regeneration of all tabs to ensure charts are updated
            self.update_all_substabs()

            # Also force regeneration of the current tab
            self.generate_current_tab(force=True)

            # Show a message to the user about what historical data is being used
            from dialog_manager import information
            info_message = f"Historical data loaded for {timestamp_str}\n"
            info_message += f"Reference price: ${historical_close_price:.2f} (close of selected day)\n"
            if historical_category:
                info_message += f"Category: {historical_category}\n"
            info_message += f"Using data from {closest_idx + 1} candles up to and including the selected day (out of {len(original_data)} total candles).\n\n"

            if self.historical_day_high is not None and self.historical_day_low is not None:
                info_message += f"Charts now show:\n"
                info_message += f"• White line at x=2.35 showing the high/low range of the NEXT day\n"
                info_message += f"• Next Day High: ${self.historical_day_high:.2f}\n"
                info_message += f"• Next Day Low: ${self.historical_day_low:.2f}"
            else:
                info_message += f"Note: No next day data available to display range line."

            information(
                self,
                "Historical Data Loaded",
                info_message
            )

        except Exception as e:
            logger.error(f"Error loading historical data: {str(e)}", exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", f"Error loading historical data: {str(e)}")

    def format_time_difference(self, seconds):
        """Format a time difference in seconds to a human-readable string

        Args:
            seconds (float): Time difference in seconds

        Returns:
            str: Formatted time difference
        """
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} minutes"
        elif seconds < 86400:
            hours = seconds / 3600
            return f"{hours:.1f} hours"
        else:
            days = seconds / 86400
            return f"{days:.1f} days"

    def update_summary_panel(self, **kwargs):
        """Update the summary panel with key information

        Args:
            **kwargs: Placeholder for future parameters
        """
        # This method is now a placeholder since the panel is empty
        # It's kept for future use if settings controls are added
        pass

    def update_crosshair_price(self, price, pos=None):
        """Update the floating price box with the current price and position.

        Args:
            price (float): The current price at the crosshair position
            pos (tuple, optional): The (x, y) position in scene coordinates
        """
        if price is not None and pos is not None:
            # Get the current tab widget
            current_tab = self.stacked_widget.currentWidget()
            if hasattr(current_tab, 'plot_widget'):
                # Get the plot widget
                plot_widget = current_tab.plot_widget

                # Remove any existing price box
                if hasattr(current_tab, 'price_box_item'):
                    plot_widget.removeItem(current_tab.price_box_item)

                # Create a text item for the price box
                price_text = f"${price:.2f}"

                # Create a text item with background - using white highlight and black text
                price_box = pg.TextItem(
                    html=f'<div style="background-color: white; color: black; padding: 5px 10px; border-radius: 5px; border: 1px solid white; font-weight: bold;">{price_text}</div>',
                    anchor=(0, 0.5)  # Anchor to left-center
                )

                # Position the box at the left edge of the plot at the current y-coordinate
                view_range = plot_widget.viewRange()
                left_edge = view_range[0][0]  # Get the left edge of the current view

                # Position the box at the left edge of the visible area
                # This ensures it's always visible as a sliding box along the y-axis
                price_box.setPos(left_edge, pos[1])

                # Add the price box to the plot
                plot_widget.addItem(price_box)

                # Store a reference to the price box
                current_tab.price_box_item = price_box

                # Connect to the view box's sigRangeChanged signal to update the position when zooming/panning
                if not hasattr(current_tab, 'range_change_connected'):
                    view_box = plot_widget.getPlotItem().getViewBox()
                    view_box.sigRangeChanged.connect(lambda: self.update_price_box_position(current_tab, pos[1]))
                    current_tab.range_change_connected = True

    def update_price_box_position(self, tab, y_pos):
        """Update the position of the price box when the view changes.

        Args:
            tab: The tab containing the price box
            y_pos (float): The y-coordinate of the price box
        """
        if hasattr(tab, 'price_box_item') and hasattr(tab, 'plot_widget'):
            # Get the current view range
            view_range = tab.plot_widget.viewRange()
            left_edge = view_range[0][0]  # Get the left edge of the current view

            # Update the position of the price box to stay at the left edge
            tab.price_box_item.setPos(left_edge, y_pos)

    def update_statistics_box(self, price_levels, latest_category=None, occurrences=None, projected_prices=None, **kwargs):
        """Update the statistics box with calculated values

        Args:
            price_levels (dict): Dictionary with calculated price levels
            latest_category (str, optional): The latest category (not used, kept for compatibility)
            occurrences (int, optional): Number of occurrences (not used, kept for compatibility)
            projected_prices (list, optional): List of dictionaries with projected price information
                Each dict should have: {'idx': int, 'type': 'high'|'low', 'price': float}
            **kwargs: Additional parameters (kept for compatibility)
        """
        # No need to update the summary panel since it's empty now

        # Unused parameters (latest_category, occurrences, kwargs) are kept for backward compatibility
        # This allows callers to pass additional parameters without breaking

        # No basic price level statistics to update as per request

        # Extract all highs and lows for count and win rate calculations
        all_highs = []
        all_lows = []

        # First try to get the raw high and low values from price_levels
        if price_levels and 'high_values' in price_levels and 'low_values' in price_levels:
            # Make sure to convert values to numeric and filter out any non-numeric values
            try:
                # Convert to numeric values, handling any non-numeric values
                high_values = price_levels['high_values']
                low_values = price_levels['low_values']

                # Log the raw values for debugging
                logger.info(f"Raw high values: {high_values[:5]} (showing first 5)")
                logger.info(f"Raw low values: {low_values[:5]} (showing first 5)")

                # Convert to numeric, handling any errors
                for h in high_values:
                    try:
                        h_val = float(h)
                        all_highs.append(h_val)
                    except (ValueError, TypeError):
                        logger.warning(f"Skipping non-numeric high value: {h}")

                for l in low_values:
                    try:
                        l_val = float(l)
                        all_lows.append(l_val)
                    except (ValueError, TypeError):
                        logger.warning(f"Skipping non-numeric low value: {l}")

                logger.info(f"Using raw high and low values from price_levels: {len(all_highs)} highs, {len(all_lows)} lows")
            except Exception as e:
                logger.error(f"Error processing high/low values: {str(e)}", exc_info=True)
                # Fall back to empty lists
                all_highs = []
                all_lows = []

        # If no valid high/low values, fall back to projected prices
        if not all_highs or not all_lows:
            if projected_prices:
                all_highs = []
                all_lows = []
                for tp in projected_prices:
                    try:
                        price_val = float(tp['price'])
                        if tp['type'] == 'high':
                            all_highs.append(price_val)
                        else:  # 'low'
                            all_lows.append(price_val)
                    except (ValueError, TypeError, KeyError):
                        logger.warning(f"Skipping invalid projected price: {tp}")
                logger.info(f"Using projected prices: {len(all_highs)} highs, {len(all_lows)} lows")
            else:
                logger.warning("No valid high/low values or projected prices available")

        total_highs = len(all_highs)
        total_lows = len(all_lows)

        # Lists to store bull and bear statistics for sorting
        all_bull_stats = []
        all_bear_stats = []

        # Update apex-related rows if apex is available
        if price_levels.get('apex') is not None:
            apex = price_levels['apex']

            # Calculate bear count for apex (lows above apex)
            bear_count_apex = sum(1 for low in all_lows if low >= apex)
            bear_count_str = f"{bear_count_apex}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_apex / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_apex_low_row,
                'label': f"Low $ at or above Apex: {apex:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': apex,
                'type': 'bear',
                'stat_id': self.bear_apex_low_row.stat_id
            })

            # Calculate bull count for apex (highs below apex)
            bull_count_apex = sum(1 for high in all_highs if high <= apex)
            bull_count_str = f"{bull_count_apex}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_apex / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_apex_high_row,
                'label': f"High $ at or below Apex: {apex:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': apex,
                'type': 'bull',
                'stat_id': self.bull_apex_high_row.stat_id
            })

        # Update high-related rows if available
        if price_levels.get('true_avg_high') is not None:
            avg_high = price_levels['true_avg_high']
            max_avg_high = price_levels.get('maxavg_high', avg_high * 1.05)
            min_avg_high = price_levels.get('minavg_high', avg_high * 0.95)

            # Bear count for MaxAvg High (lows above max_avg_high)
            bear_count_maxavg = sum(1 for low in all_lows if low >= max_avg_high)
            bear_count_str = f"{bear_count_maxavg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_maxavg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_high_maxavg_row,
                'label': f"Low $ at or above MaxAvg High: {max_avg_high:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': max_avg_high,
                'type': 'bear',
                'stat_id': self.bear_high_maxavg_row.stat_id
            })

            # Bull count for MaxAvg High (highs below max_avg_high)
            bull_count_maxavg = sum(1 for high in all_highs if high <= max_avg_high)
            bull_count_str = f"{bull_count_maxavg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_maxavg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_high_maxavg_row,
                'label': f"High $ at or below MaxAvg High: {max_avg_high:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': max_avg_high,
                'type': 'bull',
                'stat_id': self.bull_high_maxavg_row.stat_id
            })

            # Bear count for Avg High (lows above avg_high)
            bear_count_avg = sum(1 for low in all_lows if low >= avg_high)
            bear_count_str = f"{bear_count_avg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_avg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_high_avg_row,
                'label': f"Low $ at or above Avg High: {avg_high:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': avg_high,
                'type': 'bear',
                'stat_id': self.bear_high_avg_row.stat_id
            })

            # Bull count for Avg High (highs below avg_high)
            bull_count_avg = sum(1 for high in all_highs if high <= avg_high)
            bull_count_str = f"{bull_count_avg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_avg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_high_avg_row,
                'label': f"High $ at or below Avg High: {avg_high:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': avg_high,
                'type': 'bull',
                'stat_id': self.bull_high_avg_row.stat_id
            })

            # Bear count for MinAvg High (lows above min_avg_high)
            bear_count_minavg = sum(1 for low in all_lows if low >= min_avg_high)
            bear_count_str = f"{bear_count_minavg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_minavg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_high_minavg_row,
                'label': f"Low $ at or above MinAvg High: {min_avg_high:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': min_avg_high,
                'type': 'bear',
                'stat_id': self.bear_high_minavg_row.stat_id
            })

            # Bull count for MinAvg High (highs below min_avg_high)
            bull_count_minavg = sum(1 for high in all_highs if high <= min_avg_high)
            bull_count_str = f"{bull_count_minavg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_minavg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_high_minavg_row,
                'label': f"High $ at or below MinAvg High: {min_avg_high:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': min_avg_high,
                'type': 'bull',
                'stat_id': self.bull_high_minavg_row.stat_id
            })

        # Update low-related rows if available
        if price_levels.get('true_avg_low') is not None:
            avg_low = price_levels['true_avg_low']
            max_avg_low = price_levels.get('maxavg_low', avg_low * 1.05)
            min_avg_low = price_levels.get('minavg_low', avg_low * 0.95)

            # Bear count for MaxAvg Low (lows above max_avg_low)
            bear_count_maxavg = sum(1 for low in all_lows if low >= max_avg_low)
            bear_count_str = f"{bear_count_maxavg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_maxavg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_low_maxavg_row,
                'label': f"Low $ at or above MaxAvg Low: {max_avg_low:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': max_avg_low,
                'type': 'bear',
                'stat_id': self.bear_low_maxavg_row.stat_id
            })

            # Bull count for MaxAvg Low (highs below max_avg_low)
            bull_count_maxavg = sum(1 for high in all_highs if high <= max_avg_low)
            bull_count_str = f"{bull_count_maxavg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_maxavg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_low_maxavg_row,
                'label': f"High $ at or below MaxAvg Low: {max_avg_low:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': max_avg_low,
                'type': 'bull',
                'stat_id': self.bull_low_maxavg_row.stat_id
            })

            # Bear count for Avg Low (lows above avg_low)
            bear_count_avg = sum(1 for low in all_lows if low >= avg_low)
            bear_count_str = f"{bear_count_avg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_avg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_low_avg_row,
                'label': f"Low $ at or above Avg Low: {avg_low:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': avg_low,
                'type': 'bear',
                'stat_id': self.bear_low_avg_row.stat_id
            })

            # Bull count for Avg Low (highs below avg_low)
            bull_count_avg = sum(1 for high in all_highs if high <= avg_low)
            bull_count_str = f"{bull_count_avg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_avg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_low_avg_row,
                'label': f"High $ at or below Avg Low: {avg_low:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': avg_low,
                'type': 'bull',
                'stat_id': self.bull_low_avg_row.stat_id
            })

            # Bear count for MinAvg Low (lows above min_avg_low)
            bear_count_minavg = sum(1 for low in all_lows if low >= min_avg_low)
            bear_count_str = f"{bear_count_minavg}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_minavg / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_low_minavg_row,
                'label': f"Low $ at or above MinAvg Low: {min_avg_low:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': min_avg_low,
                'type': 'bear',
                'stat_id': self.bear_low_minavg_row.stat_id
            })

            # Bull count for MinAvg Low (highs below min_avg_low)
            bull_count_minavg = sum(1 for high in all_highs if high <= min_avg_low)
            bull_count_str = f"{bull_count_minavg}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_minavg / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_low_minavg_row,
                'label': f"High $ at or below MinAvg Low: {min_avg_low:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': min_avg_low,
                'type': 'bull',
                'stat_id': self.bull_low_minavg_row.stat_id
            })

        # Update median-related rows if available
        if price_levels.get('avg_high_lowest_high') is not None and price_levels.get('avg_low_highest_low') is not None:
            long_median = price_levels['avg_high_lowest_high']
            short_median = price_levels['avg_low_highest_low']

            # Bear count for Long Median (lows above long_median)
            bear_count_long = sum(1 for low in all_lows if low >= long_median)
            bear_count_str = f"{bear_count_long}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_long / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_long_median_row,
                'label': f"Low $ at or above High Median: {long_median:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': long_median,
                'type': 'bear',
                'stat_id': self.bear_long_median_row.stat_id
            })

            # Bull count for Long Median (highs below long_median)
            bull_count_long = sum(1 for high in all_highs if high <= long_median)
            bull_count_str = f"{bull_count_long}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_long / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_long_median_row,
                'label': f"High $ at or below High Median: {long_median:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': long_median,
                'type': 'bull',
                'stat_id': self.bull_long_median_row.stat_id
            })

            # Bear count for Short Median (lows above short_median)
            bear_count_short = sum(1 for low in all_lows if low >= short_median)
            bear_count_str = f"{bear_count_short}/{total_lows}" if total_lows > 0 else "--"
            bear_winrate = (bear_count_short / total_lows * 100) if total_lows > 0 else 0

            # Add to bear stats list
            all_bear_stats.append({
                'row': self.bear_short_median_row,
                'label': f"Low $ at or above Low Median: {short_median:.2f}",
                'count': bear_count_str,
                'winrate': bear_winrate,
                'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                'price': short_median,
                'type': 'bear',
                'stat_id': self.bear_short_median_row.stat_id
            })

            # Bull count for Short Median (highs below short_median)
            bull_count_short = sum(1 for high in all_highs if high <= short_median)
            bull_count_str = f"{bull_count_short}/{total_highs}" if total_highs > 0 else "--"
            bull_winrate = (bull_count_short / total_highs * 100) if total_highs > 0 else 0

            # Add to bull stats list
            all_bull_stats.append({
                'row': self.bull_short_median_row,
                'label': f"High $ at or below Low Median: {short_median:.2f}",
                'count': bull_count_str,
                'winrate': bull_winrate,
                'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                'price': short_median,
                'type': 'bull',
                'stat_id': self.bull_short_median_row.stat_id
            })

        # Get Wall Long and Wall Short values from price_levels
        wall_long = price_levels.get('wall_long', 0)
        wall_short = price_levels.get('wall_short', 0)

        # Bear count for Wall Long (lows above wall_long)
        bear_count_wall_long = sum(1 for low in all_lows if low >= wall_long)
        bear_count_str = f"{bear_count_wall_long}/{total_lows}" if total_lows > 0 else "--"
        bear_winrate = (bear_count_wall_long / total_lows * 100) if total_lows > 0 else 0

        # Add to bear stats list
        all_bear_stats.append({
            'row': self.bear_wall_long_row,
            'label': f"Low $ at or above High Barrier: {wall_long:.2f}",
            'count': bear_count_str,
            'winrate': bear_winrate,
            'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
            'price': wall_long,
            'type': 'bear',
            'stat_id': self.bear_wall_long_row.stat_id
        })

        # Bull count for Wall Long (highs below wall_long)
        bull_count_wall_long = sum(1 for high in all_highs if high <= wall_long)
        bull_count_str = f"{bull_count_wall_long}/{total_highs}" if total_highs > 0 else "--"
        bull_winrate = (bull_count_wall_long / total_highs * 100) if total_highs > 0 else 0

        # Add to bull stats list
        all_bull_stats.append({
            'row': self.bull_wall_long_row,
            'label': f"High $ at or below High Barrier: {wall_long:.2f}",
            'count': bull_count_str,
            'winrate': bull_winrate,
            'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
            'price': wall_long,
            'type': 'bull',
            'stat_id': self.bull_wall_long_row.stat_id
        })

        # Bear count for Wall Short (lows above wall_short)
        bear_count_wall_short = sum(1 for low in all_lows if low >= wall_short)
        bear_count_str = f"{bear_count_wall_short}/{total_lows}" if total_lows > 0 else "--"
        bear_winrate = (bear_count_wall_short / total_lows * 100) if total_lows > 0 else 0

        # Add to bear stats list
        all_bear_stats.append({
            'row': self.bear_wall_short_row,
            'label': f"Low $ at or above Low Barrier: {wall_short:.2f}",
            'count': bear_count_str,
            'winrate': bear_winrate,
            'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
            'price': wall_short,
            'type': 'bear',
            'stat_id': self.bear_wall_short_row.stat_id
        })

        # Bull count for Wall Short (highs below wall_short)
        bull_count_wall_short = sum(1 for high in all_highs if high <= wall_short)
        bull_count_str = f"{bull_count_wall_short}/{total_highs}" if total_highs > 0 else "--"
        bull_winrate = (bull_count_wall_short / total_highs * 100) if total_highs > 0 else 0

        # Add to bull stats list
        all_bull_stats.append({
            'row': self.bull_wall_short_row,
            'label': f"High $ at or below Low Barrier: {wall_short:.2f}",
            'count': bull_count_str,
            'winrate': bull_winrate,
            'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
            'price': wall_short,
            'type': 'bull',
            'stat_id': self.bull_wall_short_row.stat_id
        })

        # Update ETH Options Zones statistics if available
        eth_zones_data = kwargs.get('eth_zones_data', {})

        # Store ETH zones data for persistence across tab switches
        if eth_zones_data:
            self.eth_zones_data = eth_zones_data
        elif hasattr(self, 'eth_zones_data'):
            # Use previously stored ETH zones data if available
            eth_zones_data = self.eth_zones_data

        if eth_zones_data:
            # Call Peak statistics
            if 'call_peak' in eth_zones_data:
                call_peak = eth_zones_data['call_peak']
                # Bull count for Call Peak (highs below call_peak)
                bull_count_call_peak = sum(1 for high in all_highs if high <= call_peak)
                bull_count_str = f"{bull_count_call_peak}/{total_highs}" if total_highs > 0 else "--"
                bull_winrate = (bull_count_call_peak / total_highs * 100) if total_highs > 0 else 0

                all_bull_stats.append({
                    'row': self.density_call_peak_row,
                    'label': f"High $ at or below Peak Call Side ETH: {call_peak:.2f}",
                    'count': bull_count_str,
                    'winrate': bull_winrate,
                    'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                    'price': call_peak,
                    'type': 'bull',
                    'stat_id': self.density_call_peak_row.stat_id
                })

            # Put Peak statistics
            if 'put_peak' in eth_zones_data:
                put_peak = eth_zones_data['put_peak']
                # Bear count for Put Peak (lows above put_peak)
                bear_count_put_peak = sum(1 for low in all_lows if low >= put_peak)
                bear_count_str = f"{bear_count_put_peak}/{total_lows}" if total_lows > 0 else "--"
                bear_winrate = (bear_count_put_peak / total_lows * 100) if total_lows > 0 else 0

                all_bear_stats.append({
                    'row': self.density_put_peak_row,
                    'label': f"Low $ at or above Peak Put Side ETH: {put_peak:.2f}",
                    'count': bear_count_str,
                    'winrate': bear_winrate,
                    'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                    'price': put_peak,
                    'type': 'bear',
                    'stat_id': self.density_put_peak_row.stat_id
                })

            # Call Inner Wall statistics
            if 'call_inner_wall' in eth_zones_data:
                call_inner_wall = eth_zones_data['call_inner_wall']
                # Bull count for Call Inner Wall (highs below call_inner_wall)
                bull_count_call_inner_wall = sum(1 for high in all_highs if high <= call_inner_wall)
                bull_count_str = f"{bull_count_call_inner_wall}/{total_highs}" if total_highs > 0 else "--"
                bull_winrate = (bull_count_call_inner_wall / total_highs * 100) if total_highs > 0 else 0

                all_bull_stats.append({
                    'row': self.density_call_inner_wall_row,
                    'label': f"High $ at or below Inner Wall Call Side ETH: {call_inner_wall:.2f}",
                    'count': bull_count_str,
                    'winrate': bull_winrate,
                    'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                    'price': call_inner_wall,
                    'type': 'bull',
                    'stat_id': self.density_call_inner_wall_row.stat_id
                })

            # Put Inner Wall statistics
            if 'put_inner_wall' in eth_zones_data:
                put_inner_wall = eth_zones_data['put_inner_wall']
                # Bear count for Put Inner Wall (lows above put_inner_wall)
                bear_count_put_inner_wall = sum(1 for low in all_lows if low >= put_inner_wall)
                bear_count_str = f"{bear_count_put_inner_wall}/{total_lows}" if total_lows > 0 else "--"
                bear_winrate = (bear_count_put_inner_wall / total_lows * 100) if total_lows > 0 else 0

                all_bear_stats.append({
                    'row': self.density_put_inner_wall_row,
                    'label': f"Low $ at or above Inner Wall Put Side ETH: {put_inner_wall:.2f}",
                    'count': bear_count_str,
                    'winrate': bear_winrate,
                    'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                    'price': put_inner_wall,
                    'type': 'bear',
                    'stat_id': self.density_put_inner_wall_row.stat_id
                })

            # Call Wall statistics
            if 'call_wall' in eth_zones_data:
                call_wall = eth_zones_data['call_wall']
                # Bull count for Call Wall (highs below call_wall)
                bull_count_call_wall = sum(1 for high in all_highs if high <= call_wall)
                bull_count_str = f"{bull_count_call_wall}/{total_highs}" if total_highs > 0 else "--"
                bull_winrate = (bull_count_call_wall / total_highs * 100) if total_highs > 0 else 0

                all_bull_stats.append({
                    'row': self.density_call_wall_row,
                    'label': f"High $ at or below Wall Call Side ETH: {call_wall:.2f}",
                    'count': bull_count_str,
                    'winrate': bull_winrate,
                    'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                    'price': call_wall,
                    'type': 'bull',
                    'stat_id': self.density_call_wall_row.stat_id
                })

            # Put Wall statistics
            if 'put_wall' in eth_zones_data:
                put_wall = eth_zones_data['put_wall']
                # Bear count for Put Wall (lows above put_wall)
                bear_count_put_wall = sum(1 for low in all_lows if low >= put_wall)
                bear_count_str = f"{bear_count_put_wall}/{total_lows}" if total_lows > 0 else "--"
                bear_winrate = (bear_count_put_wall / total_lows * 100) if total_lows > 0 else 0

                all_bear_stats.append({
                    'row': self.density_put_wall_row,
                    'label': f"Low $ at or above Wall Put Side ETH: {put_wall:.2f}",
                    'count': bear_count_str,
                    'winrate': bear_winrate,
                    'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                    'price': put_wall,
                    'type': 'bear',
                    'stat_id': self.density_put_wall_row.stat_id
                })

            # Call Overflow statistics
            if 'call_overflow' in eth_zones_data:
                call_overflow = eth_zones_data['call_overflow']
                # Bull count for Call Overflow (highs below call_overflow)
                bull_count_call_overflow = sum(1 for high in all_highs if high <= call_overflow)
                bull_count_str = f"{bull_count_call_overflow}/{total_highs}" if total_highs > 0 else "--"
                bull_winrate = (bull_count_call_overflow / total_highs * 100) if total_highs > 0 else 0

                all_bull_stats.append({
                    'row': self.density_call_overflow_row,
                    'label': f"High $ at or below Overflow Call Side ETH: {call_overflow:.2f}",
                    'count': bull_count_str,
                    'winrate': bull_winrate,
                    'winrate_str': f"{bull_winrate:.2f}%" if total_highs > 0 else "--",
                    'price': call_overflow,
                    'type': 'bull',
                    'stat_id': self.density_call_overflow_row.stat_id
                })

            # Put Overflow statistics
            if 'put_overflow' in eth_zones_data:
                put_overflow = eth_zones_data['put_overflow']
                # Bear count for Put Overflow (lows above put_overflow)
                bear_count_put_overflow = sum(1 for low in all_lows if low >= put_overflow)
                bear_count_str = f"{bear_count_put_overflow}/{total_lows}" if total_lows > 0 else "--"
                bear_winrate = (bear_count_put_overflow / total_lows * 100) if total_lows > 0 else 0

                all_bear_stats.append({
                    'row': self.density_put_overflow_row,
                    'label': f"Low $ at or above Overflow Put Side ETH: {put_overflow:.2f}",
                    'count': bear_count_str,
                    'winrate': bear_winrate,
                    'winrate_str': f"{bear_winrate:.2f}%" if total_lows > 0 else "--",
                    'price': put_overflow,
                    'type': 'bear',
                    'stat_id': self.density_put_overflow_row.stat_id
                })

        # Sort bull and bear stats by win rate (highest to lowest)

        # Sort bull stats by win rate (highest to lowest)
        all_bull_stats.sort(key=lambda x: x['winrate'], reverse=True)

        # Sort bear stats by win rate (highest to lowest)
        all_bear_stats.sort(key=lambda x: x['winrate'], reverse=True)

        # Combine all stats with bull stats first, then bear stats
        all_stats = all_bull_stats + all_bear_stats

        # Store the stats data for use by the graph tabs
        self.stats_data = []

        # Add all stats to the stats_data list with win rate information
        for stat in all_stats:
            self.stats_data.append({
                'price': stat['price'],
                'type': stat['type'],
                'stat_id': stat['stat_id'],
                'winrate': stat['winrate'],
                'winrate_str': stat['winrate_str']
            })

        # Remove all rows from the layout
        layout = self.stats_container.layout()
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().hide()

        # Add rows back in the correct order
        for stat in all_stats:
            layout.addWidget(stat['row'])
            stat['row'].show()
            stat['row'].stat_label.setText(stat['label'])
            stat['row'].count_label.setText(stat['count'])
            stat['row'].winrate_label.setText(stat['winrate_str'])

        # Clear existing projected price labels
        for label in self.projected_labels:
            label.setParent(None)
            label.deleteLater()
        self.projected_labels = []

        # Add projected price labels if provided
        if projected_prices:
            # Calculate total number of projected prices for win rate calculation
            total_projected_prices = len(projected_prices)

            # Lists to store long and short projected prices for sorting
            long_projected = []
            short_projected = []

            # Process each projected price
            for tp in projected_prices:
                # Calculate count and win rate based on the price
                if tp['type'] == 'high':
                    # For Short projected price (high), count all prices at or below this price
                    count_below = sum(1 for p in projected_prices if p['price'] <= tp['price'])
                    count = f"{count_below}/{total_projected_prices}"
                    win_rate = (count_below / total_projected_prices) * 100
                    text = f"Projected High price $ at or below {tp['price']:.2f}"

                    # Add to short projected list
                    short_projected.append({
                        'text': text,
                        'count': count,
                        'win_rate': win_rate,
                        'win_rate_str': f"{win_rate:.2f}%",
                        'price': tp['price'],
                        'type': 'short',  # Using 'short' instead of 'high' for clarity
                        'idx': tp.get('idx', 0)  # Include the index for reference
                    })
                else:  # 'low'
                    # For Long projected price (low), count all prices at or above this price
                    count_above = sum(1 for p in projected_prices if p['price'] >= tp['price'])
                    count = f"{count_above}/{total_projected_prices}"
                    win_rate = (count_above / total_projected_prices) * 100
                    text = f"Projected Low price $ at or above {tp['price']:.2f}"

                    # Add to long projected list
                    long_projected.append({
                        'text': text,
                        'count': count,
                        'win_rate': win_rate,
                        'win_rate_str': f"{win_rate:.2f}%",
                        'price': tp['price'],
                        'type': 'long',  # Using 'long' instead of 'low' for clarity
                        'idx': tp.get('idx', 0)  # Include the index for reference
                    })

            # Sort long projected prices by win rate (highest to lowest)
            long_projected.sort(key=lambda x: x['win_rate'], reverse=True)

            # Sort short projected prices by win rate (highest to lowest)
            short_projected.sort(key=lambda x: x['win_rate'], reverse=True)

            # Create and add labels for short projected prices first
            for tp in short_projected:
                # Create a row with three columns for the projected price
                row_widget = QtWidgets.QWidget()
                row_layout = QtWidgets.QHBoxLayout(row_widget)
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(10)

                # Create a toggle checkbox
                toggle_checkbox = QtWidgets.QCheckBox()
                # Generate a unique stat_id for the projected price
                stat_id = f"projected_short_{tp['idx']}_{int(tp['price'] * 100)}"
                # Set the initial state based on the toggled_stats dictionary
                toggle_checkbox.setChecked(self.toggled_stats.get(stat_id, False))
                toggle_checkbox.setFixedWidth(20)
                toggle_checkbox.setStyleSheet(f"""
                    QCheckBox::indicator {{
                        width: 15px;
                        height: 15px;
                        border-radius: 3px;
                    }}
                    QCheckBox::indicator:checked {{
                        background-color: {THEME_COLORS['primary_accent']};
                        border: 2px solid {THEME_COLORS['text']};
                    }}
                    QCheckBox::indicator:unchecked {{
                        background-color: {THEME_COLORS['background']};
                        border: 2px solid {THEME_COLORS['borders']};
                    }}
                """)

                # Set the stat_id property on the checkbox
                toggle_checkbox.setProperty("stat_id", stat_id)

                # Connect the toggle to the handler
                toggle_checkbox.toggled.connect(self.on_stat_toggle_changed)

                # Create labels for each column
                price_label = QtWidgets.QLabel(tp['text'])
                count_label = QtWidgets.QLabel(tp['count'])
                winrate_label = QtWidgets.QLabel(tp['win_rate_str'])

                # Style the labels
                label_style = f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace;"
                price_label.setStyleSheet(label_style)
                count_label.setStyleSheet(label_style)
                winrate_label.setStyleSheet(label_style)

                # Enable word wrapping
                price_label.setWordWrap(True)

                # Store references to the labels and toggle for later updates
                row_widget.stat_label = price_label
                row_widget.count_label = count_label
                row_widget.winrate_label = winrate_label
                row_widget.toggle_checkbox = toggle_checkbox
                row_widget.stat_id = stat_id

                # Store the price and type for plotting
                tp['stat_id'] = stat_id

                # Add toggle and labels to the row layout with appropriate stretch factors
                row_layout.addWidget(toggle_checkbox, 0)  # No stretch for checkbox
                row_layout.addWidget(price_label, 3)  # Larger width for price
                row_layout.addWidget(count_label, 1)
                row_layout.addWidget(winrate_label, 1)

                # Add the row to the projected layout
                self.projected_labels.append(row_widget)
                self.projected_layout.addWidget(row_widget)

                # Add to stats_data for plotting
                self.stats_data.append({
                    'price': tp['price'],
                    'type': 'short',  # Using 'short' instead of 'high' for clarity
                    'stat_id': stat_id,
                    'winrate': tp['win_rate'],
                    'winrate_str': tp['win_rate_str']
                })

            # Create and add labels for long projected prices next
            for tp in long_projected:
                # Create a row with three columns for the projected price
                row_widget = QtWidgets.QWidget()
                row_layout = QtWidgets.QHBoxLayout(row_widget)
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(10)

                # Create a toggle checkbox
                toggle_checkbox = QtWidgets.QCheckBox()
                # Generate a unique stat_id for the projected price
                stat_id = f"projected_long_{tp['idx']}_{int(tp['price'] * 100)}"
                # Set the initial state based on the toggled_stats dictionary
                toggle_checkbox.setChecked(self.toggled_stats.get(stat_id, False))
                toggle_checkbox.setFixedWidth(20)
                toggle_checkbox.setStyleSheet(f"""
                    QCheckBox::indicator {{
                        width: 15px;
                        height: 15px;
                        border-radius: 3px;
                    }}
                    QCheckBox::indicator:checked {{
                        background-color: {THEME_COLORS['primary_accent']};
                        border: 2px solid {THEME_COLORS['text']};
                    }}
                    QCheckBox::indicator:unchecked {{
                        background-color: {THEME_COLORS['background']};
                        border: 2px solid {THEME_COLORS['borders']};
                    }}
                """)

                # Set the stat_id property on the checkbox
                toggle_checkbox.setProperty("stat_id", stat_id)

                # Connect the toggle to the handler
                toggle_checkbox.toggled.connect(self.on_stat_toggle_changed)

                # Create labels for each column
                price_label = QtWidgets.QLabel(tp['text'])
                count_label = QtWidgets.QLabel(tp['count'])
                winrate_label = QtWidgets.QLabel(tp['win_rate_str'])

                # Style the labels
                label_style = f"color: {THEME_COLORS['text']}; font-family: 'Consolas', 'Courier New', monospace;"
                price_label.setStyleSheet(label_style)
                count_label.setStyleSheet(label_style)
                winrate_label.setStyleSheet(label_style)

                # Enable word wrapping
                price_label.setWordWrap(True)

                # Store references to the labels and toggle for later updates
                row_widget.stat_label = price_label
                row_widget.count_label = count_label
                row_widget.winrate_label = winrate_label
                row_widget.toggle_checkbox = toggle_checkbox
                row_widget.stat_id = stat_id

                # Store the price and type for plotting
                tp['stat_id'] = stat_id

                # Add toggle and labels to the row layout with appropriate stretch factors
                row_layout.addWidget(toggle_checkbox, 0)  # No stretch for checkbox
                row_layout.addWidget(price_label, 3)  # Larger width for price
                row_layout.addWidget(count_label, 1)
                row_layout.addWidget(winrate_label, 1)

                # Add the row to the projected layout
                self.projected_labels.append(row_widget)
                self.projected_layout.addWidget(row_widget)

                # Add to stats_data for plotting
                self.stats_data.append({
                    'price': tp['price'],
                    'type': 'long',  # Using 'long' instead of 'low' for clarity
                    'stat_id': stat_id,
                    'winrate': tp['win_rate'],
                    'winrate_str': tp['win_rate_str']
                })

    def ak_weekly_vol_zones(self):
        """
        AK's weekly vol zones functionality.
        Toggles between normal volatility levels and AK's special weekly vol zones.
        When active, calculates levels with FWL agg=5 and weekday matching.
        """
        try:
            # Toggle the AK weekly vol zones state
            self.ak_weekly_vol_zones_active = not self.ak_weekly_vol_zones_active

            if self.ak_weekly_vol_zones_active:
                logger.info("Activating AK's weekly vol zones")

                # Update button appearance to show it's active
                self.ak_weekly_vol_zones_button.setText("Deactivate AK Zones")
                self.ak_weekly_vol_zones_button.setStyleSheet(f"""
                    QPushButton {{
                        color: {THEME_COLORS['background']};
                        background-color: {THEME_COLORS['highlight']};
                        border: 1px solid {THEME_COLORS['highlight']};
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-weight: bold;
                        font-size: 11px;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background-color: {THEME_COLORS['pressed_accent']};
                        border: 1px solid {THEME_COLORS['pressed_accent']};
                    }}
                    QPushButton:pressed {{
                        background-color: {THEME_COLORS['pressed_accent']};
                        color: {THEME_COLORS['background']};
                    }}
                """)

                # Calculate AK's weekly vol zones
                self.calculate_ak_weekly_vol_zones()

            else:
                logger.info("Deactivating AK's weekly vol zones")

                # Reset button appearance to normal
                self.ak_weekly_vol_zones_button.setText("Activate AK Zones")
                ak_zones_button_style = f"""
                    QPushButton {{
                        color: {THEME_COLORS['text']};
                        background-color: {THEME_COLORS['control_panel']};
                        border: 1px solid {THEME_COLORS['borders']};
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-weight: bold;
                        font-size: 11px;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background-color: {THEME_COLORS['borders']};
                        border: 1px solid white;
                    }}
                    QPushButton:pressed {{
                        background-color: white;
                        color: {THEME_COLORS['background']};
                    }}
                """
                self.ak_weekly_vol_zones_button.setStyleSheet(ak_zones_button_style)

                # Clear AK levels
                self.ak_fwl5_levels = {}
                self.ak_weekday_levels = {}

            # Trigger update of all tabs to show/hide AK zones
            self.update_all_tabs_ak_zones()

        except Exception as e:
            logger.error(f"Error in AK's weekly vol zones: {str(e)}", exc_info=True)

    def calculate_ak_weekly_vol_zones(self):
        """
        Calculate AK's weekly vol zones by:
        1. Setting FWL agg to 5 and calculating H/L matching levels
        2. Setting to weekday matching and calculating weekday levels
        3. Combining both sets of levels for display
        """
        try:
            # Store original settings
            original_fwl_agg = int(self.numeric_input.text())
            original_matching_mode = 'hl' if self.hl_matching_btn.isChecked() else 'weekday'

            logger.info(f"Starting AK's weekly vol zones calculation")
            logger.info(f"Original settings - FWL Agg: {original_fwl_agg}, Matching: {original_matching_mode}")

            # Step 1: Set FWL agg to 5 and ensure H/L matching is active
            logger.info("Step 1: Setting FWL agg to 5 and using H/L matching")
            self.numeric_input.setText("5")

            # Ensure H/L matching is selected
            if not self.hl_matching_btn.isChecked():
                self.hl_matching_btn.setChecked(True)

            # Wait longer for the FWL change to propagate and data to be recalculated
            QtCore.QTimer.singleShot(500, lambda: self.calculate_hl_fwl5_levels_after_data_refresh(original_fwl_agg, original_matching_mode))

        except Exception as e:
            logger.error(f"Error calculating AK's weekly vol zones: {str(e)}", exc_info=True)

    def calculate_hl_fwl5_levels_after_data_refresh(self, original_fwl_agg, original_matching_mode):
        """Calculate H/L matching levels with FWL agg = 5 after data has been refreshed"""
        try:
            # Get fresh data from the Data tab (should now be calculated with FWL agg = 5)
            data = self.volatility_graph_tab.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available for H/L FWL5 calculation")
                self.ak_fwl5_levels = {}
            else:
                # Verify that we're using FWL agg = 5
                current_fwl = int(self.numeric_input.text())
                logger.info(f"Current FWL agg setting: {current_fwl}")

                # Get H/L matching rows (should be active now)
                latest_category = self.volatility_graph_tab.get_latest_category(data)
                if latest_category:
                    matching_rows = self.volatility_graph_tab.get_rows_with_same_category(data, latest_category)
                else:
                    matching_rows = pd.DataFrame()

                if not matching_rows.empty:
                    self.ak_fwl5_levels = self.volatility_graph_tab.calculate_price_levels(matching_rows)
                    logger.info(f"Calculated H/L FWL5 levels: {len(self.ak_fwl5_levels)} levels")
                    logger.info(f"Sample H/L FWL5 levels: {dict(list(self.ak_fwl5_levels.items())[:3])}")
                else:
                    logger.warning("No matching rows for H/L FWL5 calculation")
                    self.ak_fwl5_levels = {}

            # Step 2: Switch to weekday matching and calculate levels
            logger.info("Step 2: Switching to weekday matching (keeping FWL agg = 5)")
            if not self.weekday_matching_btn.isChecked():
                self.weekday_matching_btn.setChecked(True)

            # Wait for weekday matching to take effect and data to refresh
            QtCore.QTimer.singleShot(500, lambda: self.calculate_weekday_fwl5_levels_after_data_refresh(original_fwl_agg, original_matching_mode))

        except Exception as e:
            logger.error(f"Error calculating H/L FWL5 levels: {str(e)}", exc_info=True)

    def calculate_weekday_fwl5_levels_after_data_refresh(self, original_fwl_agg, original_matching_mode):
        """Calculate weekday matching levels with FWL agg = 5 after data has been refreshed"""
        try:
            # Get fresh data from the Data tab (should now be calculated with FWL agg = 5 and weekday matching)
            data = self.volatility_graph_tab.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available for weekday FWL5 calculation")
                self.ak_weekday_levels = {}
            else:
                # Verify that we're still using FWL agg = 5
                current_fwl = int(self.numeric_input.text())
                current_matching = 'hl' if self.hl_matching_btn.isChecked() else 'weekday'
                logger.info(f"Current settings - FWL agg: {current_fwl}, Matching: {current_matching}")

                # Get weekday matching rows (FWL agg should still be 5)
                matching_rows = self.volatility_graph_tab.get_rows_with_same_weekday(data)

                if not matching_rows.empty:
                    self.ak_weekday_levels = self.volatility_graph_tab.calculate_price_levels(matching_rows)
                    logger.info(f"Calculated weekday FWL5 levels: {len(self.ak_weekday_levels)} levels")
                    logger.info(f"Sample weekday FWL5 levels: {dict(list(self.ak_weekday_levels.items())[:3])}")
                else:
                    logger.warning("No matching rows for weekday FWL5 calculation")
                    self.ak_weekday_levels = {}

            # Step 3: Restore original settings
            logger.info(f"Step 3: Restoring original settings - FWL Agg: {original_fwl_agg}, Matching: {original_matching_mode}")

            # Restore FWL agg
            self.numeric_input.setText(str(original_fwl_agg))

            # Restore matching mode
            if original_matching_mode == 'hl':
                self.hl_matching_btn.setChecked(True)
            else:
                self.weekday_matching_btn.setChecked(True)

            logger.info("AK's weekly vol zones calculation completed")
            logger.info(f"Total AK levels - H/L FWL5: {len(self.ak_fwl5_levels)}, Weekday FWL5: {len(self.ak_weekday_levels)}")



            # Trigger candlestick chart update to show the new zones
            self.update_all_tabs_ak_zones()

        except Exception as e:
            logger.error(f"Error calculating weekday FWL5 levels: {str(e)}", exc_info=True)

    def update_all_tabs_ak_zones(self):
        """Update all tabs to show/hide AK zones"""
        try:
            # Update volatility graph tab
            if hasattr(self.volatility_graph_tab, 'ak_weekly_vol_zones_active'):
                self.volatility_graph_tab.ak_weekly_vol_zones_active = self.ak_weekly_vol_zones_active
                self.volatility_graph_tab.ak_fwl5_levels = self.ak_fwl5_levels
                self.volatility_graph_tab.ak_weekday_levels = self.ak_weekday_levels
                # Note: density peaks/troughs are accessed directly from main tab via get_main_volatility_tab()

                # Trigger candlestick chart update
                if hasattr(self.volatility_graph_tab, 'update_candlestick_volatility_levels'):
                    self.volatility_graph_tab.update_candlestick_volatility_levels()

            # Update density graph tab if it has AK zones functionality
            if hasattr(self.density_graph_tab, 'ak_weekly_vol_zones_active'):
                self.density_graph_tab.ak_weekly_vol_zones_active = self.ak_weekly_vol_zones_active
                self.density_graph_tab.ak_fwl5_levels = self.ak_fwl5_levels
                self.density_graph_tab.ak_weekday_levels = self.ak_weekday_levels

                # If AK zones are active, trigger density graph regeneration to collect peaks/troughs
                if self.ak_weekly_vol_zones_active:
                    self.density_graph_tab.generate_density_graph()

            # Update FWL odds tab if it has AK zones functionality
            if hasattr(self.fwl_odds_tab, 'ak_weekly_vol_zones_active'):
                self.fwl_odds_tab.ak_weekly_vol_zones_active = self.ak_weekly_vol_zones_active
                self.fwl_odds_tab.ak_fwl5_levels = self.ak_fwl5_levels
                self.fwl_odds_tab.ak_weekday_levels = self.ak_weekday_levels

        except Exception as e:
            logger.error(f"Error updating tabs with AK zones: {str(e)}", exc_info=True)

    def ak_daily_vol_zones(self):
        """
        AK's daily vol zones functionality.
        Loads and fetches data for 200 DTL, 250 DTL, and 500 DTL charts consecutively.
        Caches the volatility graphs for each DTL value.
        """
        try:
            # Toggle the AK daily vol zones state
            self.ak_daily_vol_zones_active = not self.ak_daily_vol_zones_active

            if self.ak_daily_vol_zones_active:
                logger.info("Activating AK's daily vol zones - loading 200, 250, 500 DTL data")

                # Update button text to show loading state
                self.ak_daily_vol_zones_button.setText("Loading DTL Data...")
                self.ak_daily_vol_zones_button.setEnabled(False)

                # Start the consecutive data loading process
                self.load_consecutive_dtl_data()
            else:
                logger.info("Deactivating AK's daily vol zones")

                # Clear cached data
                self.ak_daily_vol_zones_data.clear()
                self.ak_daily_vol_zones_cache.clear()

                # Update button text
                self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
                self.ak_daily_vol_zones_button.setEnabled(True)

                # Update all tabs to remove AK daily zones
                self.update_all_tabs_ak_daily_zones()

        except Exception as e:
            logger.error(f"Error in ak_daily_vol_zones: {str(e)}", exc_info=True)
            # Reset button state on error
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def load_consecutive_dtl_data(self):
        """
        Load data consecutively for 200 DTL, 250 DTL, and 500 DTL.
        """
        try:
            from parameter_registry import default_registry
            from data_dispatcher import DataDispatcher

            # Store original DTL value
            self.original_dtl = default_registry.get_value('days_to_load')

            # Get current symbol and timeframe
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')

            if not symbol or not timeframe:
                logger.error("Symbol or timeframe not available for DTL data loading")
                self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
                self.ak_daily_vol_zones_button.setEnabled(True)
                return

            # DTL values to load consecutively
            self.dtl_values = [200, 250, 500]
            self.current_dtl_index = 0
            self.loaded_dtl_data = {}

            # Start loading the first DTL value
            self.load_next_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in load_consecutive_dtl_data: {str(e)}", exc_info=True)
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def load_next_dtl_data(self, symbol, timeframe):
        """
        Load data for the next DTL value in the sequence.
        """
        try:
            if self.current_dtl_index >= len(self.dtl_values):
                # All DTL data loaded, process and cache volatility graphs
                self.process_loaded_dtl_data()
                return

            dtl_value = self.dtl_values[self.current_dtl_index]
            logger.info(f"Loading DTL {dtl_value} data ({self.current_dtl_index + 1}/{len(self.dtl_values)})")

            # Update button text to show progress
            self.ak_daily_vol_zones_button.setText(f"Loading {dtl_value} DTL ({self.current_dtl_index + 1}/{len(self.dtl_values)})...")

            # Update the parameter registry with the new DTL value
            from parameter_registry import default_registry
            default_registry.set_value('days_to_load', dtl_value)

            # Get data dispatcher instance
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()

            # Connect to data ready signal for this specific request
            dispatcher.data_fetched.connect(self.on_dtl_data_ready)

            # Fetch data for this DTL value
            dispatcher.fetch_data(symbol, timeframe, dtl_value)

        except Exception as e:
            logger.error(f"Error in load_next_dtl_data: {str(e)}", exc_info=True)
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def on_dtl_data_ready(self, symbol, data):
        """
        Handle data ready signal for DTL loading.
        """
        try:
            # Disconnect the signal to avoid multiple connections
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.data_fetched.disconnect(self.on_dtl_data_ready)

            # Store the loaded data
            dtl_value = self.dtl_values[self.current_dtl_index]
            self.loaded_dtl_data[dtl_value] = data.copy() if data is not None else None

            logger.info(f"DTL {dtl_value} data loaded successfully")

            # Generate and cache volatility graph for this DTL
            self.cache_volatility_graph_for_dtl(dtl_value, data)

            # Move to next DTL value
            self.current_dtl_index += 1

            # Load next DTL data
            from parameter_registry import default_registry
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')
            self.load_next_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in on_dtl_data_ready: {str(e)}", exc_info=True)
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def cache_volatility_graph_for_dtl(self, dtl_value, data):
        """
        Cache the volatility levels that the volatility graph generates for the specified DTL value.
        Simply captures what's already being calculated and displayed.
        """
        try:
            if data is None or data.empty:
                logger.warning(f"No data available for DTL {dtl_value} volatility caching")
                return

            # Store the data temporarily
            temp_data_key = f"dtl_{dtl_value}_data"
            self.ak_daily_vol_zones_cache[temp_data_key] = data.copy()

            logger.info(f"Caching volatility levels for DTL {dtl_value}")

            # Temporarily change DTL to this value and trigger volatility graph generation
            from parameter_registry import default_registry
            original_dtl = default_registry.get_value('days_to_load')

            # Set the DTL value
            default_registry.set_value('days_to_load', dtl_value)

            # Trigger volatility graph generation to get the levels
            self.volatility_graph_tab.generate_volatility_graph()

            # Get the generated volatility levels from the volatility graph tab
            volatility_levels = self.volatility_graph_tab.latest_price_levels.copy()

            # Restore original DTL
            default_registry.set_value('days_to_load', original_dtl)

            # Cache the volatility levels
            cache_key = f"dtl_{dtl_value}_volatility"
            self.ak_daily_vol_zones_cache[cache_key] = volatility_levels.copy() if volatility_levels else {}

            # Debug: Log the cached levels
            if volatility_levels:
                logger.info(f"DTL {dtl_value} cached levels: {list(volatility_levels.keys())}")
                level_summary = {k: v for k, v in volatility_levels.items() if k not in ['high_values', 'low_values'] and v is not None}
                logger.info(f"DTL {dtl_value} sample values: {dict(list(level_summary.items())[:5])}")
            else:
                logger.warning(f"DTL {dtl_value}: No volatility levels generated")

            logger.info(f"Successfully cached volatility levels for DTL {dtl_value}: {len(volatility_levels) if volatility_levels else 0} levels")

        except Exception as e:
            logger.error(f"Error caching volatility graph for DTL {dtl_value}: {str(e)}", exc_info=True)

    def process_loaded_dtl_data(self):
        """
        Process all loaded DTL data and finalize the AK daily vol zones.
        Then automatically start weekday matching processing.
        """
        try:
            # Store all loaded data in the main data structure
            self.ak_daily_vol_zones_data = self.loaded_dtl_data.copy()

            # Log completion with detailed cache information
            loaded_dtls = list(self.ak_daily_vol_zones_data.keys())
            cached_graphs = [k for k in self.ak_daily_vol_zones_cache.keys() if 'volatility' in k and 'weekday' not in k]
            logger.info(f"AK's daily vol zones H/L matching completed. Loaded DTLs: {loaded_dtls}, Cached graphs: {len(cached_graphs)}")

            # Debug: Log detailed cache contents
            for key, value in self.ak_daily_vol_zones_cache.items():
                if 'volatility' in key and 'weekday' not in key:
                    logger.info(f"Cache {key}: {len(value) if value else 0} levels")
                    if value:
                        level_summary = {k: v for k, v in value.items() if k not in ['high_values', 'low_values'] and v is not None}
                        logger.info(f"  Levels: {list(level_summary.keys())}")

            # Now automatically start weekday matching processing
            logger.info("Starting automatic weekday matching processing for AK daily vol zones")
            self.ak_daily_vol_zones_button.setText("Loading Weekday Matching...")

            # Start weekday matching processing
            self.start_weekday_matching_processing()

        except Exception as e:
            logger.error(f"Error in process_loaded_dtl_data: {str(e)}", exc_info=True)
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def update_all_tabs_ak_daily_zones(self):
        """Update all tabs to show/hide AK daily zones data"""
        try:
            # Update volatility graph tab
            if hasattr(self.volatility_graph_tab, 'ak_daily_vol_zones_active'):
                self.volatility_graph_tab.ak_daily_vol_zones_active = self.ak_daily_vol_zones_active
                self.volatility_graph_tab.ak_daily_vol_zones_data = self.ak_daily_vol_zones_data
                self.volatility_graph_tab.ak_daily_vol_zones_cache = self.ak_daily_vol_zones_cache

                # Trigger volatility graph update if active
                if self.ak_daily_vol_zones_active and hasattr(self.volatility_graph_tab, 'update_candlestick_volatility_levels'):
                    self.volatility_graph_tab.update_candlestick_volatility_levels()

            # Update density graph tab
            if hasattr(self.density_graph_tab, 'ak_daily_vol_zones_active'):
                self.density_graph_tab.ak_daily_vol_zones_active = self.ak_daily_vol_zones_active
                self.density_graph_tab.ak_daily_vol_zones_data = self.ak_daily_vol_zones_data
                self.density_graph_tab.ak_daily_vol_zones_cache = self.ak_daily_vol_zones_cache

            # Update FWL odds tab
            if hasattr(self.fwl_odds_tab, 'ak_daily_vol_zones_active'):
                self.fwl_odds_tab.ak_daily_vol_zones_active = self.ak_daily_vol_zones_active
                self.fwl_odds_tab.ak_daily_vol_zones_data = self.ak_daily_vol_zones_data
                self.fwl_odds_tab.ak_daily_vol_zones_cache = self.ak_daily_vol_zones_cache

        except Exception as e:
            logger.error(f"Error updating tabs with AK daily zones: {str(e)}", exc_info=True)

    def start_weekday_matching_processing(self):
        """
        Start the weekday matching processing for AK daily vol zones.
        This switches to weekday matching mode and processes the same DTL values.
        """
        try:
            # Store original matching mode
            self.original_matching_mode = 'hl' if self.hl_matching_btn.isChecked() else 'weekday'
            logger.info(f"Starting weekday matching processing. Original mode: {self.original_matching_mode}")

            # Switch to weekday matching
            if not self.weekday_matching_btn.isChecked():
                logger.info("Switching to weekday matching mode")
                self.weekday_matching_btn.setChecked(True)

            # Initialize weekday processing variables
            self.weekday_dtl_values = [200, 250, 500]
            self.current_weekday_dtl_index = 0

            # Wait for weekday matching to take effect, then start processing
            QtCore.QTimer.singleShot(500, self.load_next_weekday_dtl_data)

        except Exception as e:
            logger.error(f"Error starting weekday matching processing: {str(e)}", exc_info=True)
            self.finalize_ak_daily_zones()

    def load_next_weekday_dtl_data(self):
        """
        Load weekday matching data for the next DTL value in the sequence.
        This actually fetches new data for each DTL while in weekday matching mode.
        """
        try:
            if self.current_weekday_dtl_index >= len(self.weekday_dtl_values):
                # All weekday DTL data loaded, finalize
                self.finalize_ak_daily_zones()
                return

            dtl_value = self.weekday_dtl_values[self.current_weekday_dtl_index]
            logger.info(f"Loading weekday matching DTL {dtl_value} data ({self.current_weekday_dtl_index + 1}/{len(self.weekday_dtl_values)})")

            # Update button text to show progress
            self.ak_daily_vol_zones_button.setText(f"Weekday {dtl_value} DTL ({self.current_weekday_dtl_index + 1}/{len(self.weekday_dtl_values)})...")

            # Update the parameter registry with the new DTL value
            from parameter_registry import default_registry
            default_registry.set_value('days_to_load', dtl_value)

            # Get data dispatcher instance and fetch fresh data for this DTL in weekday mode
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()

            # Connect to data ready signal for this specific weekday request
            dispatcher.data_fetched.connect(self.on_weekday_dtl_data_ready)

            # Get current symbol and timeframe
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')

            # Fetch data for this DTL value (will be processed in weekday matching mode)
            dispatcher.fetch_data(symbol, timeframe, dtl_value)

        except Exception as e:
            logger.error(f"Error in load_next_weekday_dtl_data: {str(e)}", exc_info=True)
            self.finalize_ak_daily_zones()

    def on_weekday_dtl_data_ready(self, symbol, data):
        """
        Handle data ready signal for weekday DTL loading.
        """
        try:
            # Disconnect the signal to avoid multiple connections
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.data_fetched.disconnect(self.on_weekday_dtl_data_ready)

            # Get current DTL value
            dtl_value = self.weekday_dtl_values[self.current_weekday_dtl_index]
            logger.info(f"Weekday DTL {dtl_value} data loaded successfully")

            # Generate and cache volatility graph for this DTL in weekday mode
            self.cache_weekday_volatility_for_dtl_with_data(dtl_value, data)

            # Move to next DTL value
            self.current_weekday_dtl_index += 1

            # Load next DTL data
            QtCore.QTimer.singleShot(300, self.load_next_weekday_dtl_data)

        except Exception as e:
            logger.error(f"Error in on_weekday_dtl_data_ready: {str(e)}", exc_info=True)
            self.finalize_ak_daily_zones()

    def cache_weekday_volatility_for_dtl(self, dtl_value):
        """
        Cache weekday matching volatility levels for the specified DTL value.
        Simply captures what the volatility graph generates when in weekday matching mode.
        """
        try:
            logger.info(f"Caching weekday volatility levels for DTL {dtl_value}")

            # Temporarily change DTL to this value and trigger volatility graph generation
            from parameter_registry import default_registry
            original_dtl = default_registry.get_value('days_to_load')

            # Set the DTL value
            default_registry.set_value('days_to_load', dtl_value)

            # Trigger volatility graph generation (should be in weekday matching mode already)
            self.volatility_graph_tab.generate_volatility_graph()

            # Get the generated volatility levels from the volatility graph tab
            volatility_levels = self.volatility_graph_tab.latest_price_levels.copy()

            # Restore original DTL
            default_registry.set_value('days_to_load', original_dtl)

            # Cache the weekday volatility levels
            cache_key = f"dtl_{dtl_value}_weekday_volatility"
            self.ak_daily_vol_zones_cache[cache_key] = volatility_levels.copy() if volatility_levels else {}

            # Debug: Log the cached levels
            if volatility_levels:
                logger.info(f"DTL {dtl_value} weekday cached levels: {list(volatility_levels.keys())}")
                level_summary = {k: v for k, v in volatility_levels.items() if k not in ['high_values', 'low_values'] and v is not None}
                logger.info(f"DTL {dtl_value} weekday sample values: {dict(list(level_summary.items())[:5])}")
            else:
                logger.warning(f"DTL {dtl_value}: No weekday volatility levels generated")

            logger.info(f"Successfully cached weekday volatility levels for DTL {dtl_value}: {len(volatility_levels) if volatility_levels else 0} levels")

        except Exception as e:
            logger.error(f"Error caching weekday volatility for DTL {dtl_value}: {str(e)}", exc_info=True)

    def cache_weekday_volatility_for_dtl_with_data(self, dtl_value, data):
        """
        Cache weekday matching volatility levels for the specified DTL value using fresh data.
        This method is called after fresh data has been fetched for the DTL in weekday matching mode.
        """
        try:
            if data is None or data.empty:
                logger.warning(f"No data available for weekday DTL {dtl_value} volatility caching")
                cache_key = f"dtl_{dtl_value}_weekday_volatility"
                self.ak_daily_vol_zones_cache[cache_key] = {}
                return

            logger.info(f"Caching weekday volatility levels for DTL {dtl_value} with fresh data ({len(data)} rows)")

            # Trigger volatility graph generation with the fresh data (should be in weekday matching mode)
            # Temporarily set the data in the volatility graph tab
            original_data = getattr(self.volatility_graph_tab, 'data', None)
            self.volatility_graph_tab.data = data

            # Generate volatility graph (this will use weekday matching since we're in that mode)
            self.volatility_graph_tab.generate_volatility_graph()

            # Get the generated volatility levels from the volatility graph tab
            volatility_levels = self.volatility_graph_tab.latest_price_levels.copy()

            # Restore original data
            if original_data is not None:
                self.volatility_graph_tab.data = original_data

            # Cache the weekday volatility levels
            cache_key = f"dtl_{dtl_value}_weekday_volatility"
            self.ak_daily_vol_zones_cache[cache_key] = volatility_levels.copy() if volatility_levels else {}

            # Debug: Log the cached levels
            if volatility_levels:
                logger.info(f"DTL {dtl_value} weekday cached levels: {list(volatility_levels.keys())}")
                level_summary = {k: v for k, v in volatility_levels.items() if k not in ['high_values', 'low_values'] and v is not None}
                logger.info(f"DTL {dtl_value} weekday sample values: {dict(list(level_summary.items())[:5])}")
            else:
                logger.warning(f"DTL {dtl_value}: No weekday volatility levels generated")

            logger.info(f"Successfully cached weekday volatility levels for DTL {dtl_value}: {len(volatility_levels) if volatility_levels else 0} levels")

        except Exception as e:
            logger.error(f"Error caching weekday volatility for DTL {dtl_value} with data: {str(e)}", exc_info=True)

    def finalize_ak_daily_zones(self):
        """
        Finalize the AK daily vol zones processing after both H/L and weekday matching are complete.
        """
        try:
            # Restore original DTL value
            from parameter_registry import default_registry
            if hasattr(self, 'original_dtl') and self.original_dtl is not None:
                default_registry.set_value('days_to_load', self.original_dtl)

            # Restore original matching mode
            if hasattr(self, 'original_matching_mode'):
                if self.original_matching_mode == 'hl':
                    self.hl_matching_btn.setChecked(True)
                else:
                    self.weekday_matching_btn.setChecked(True)
                logger.info(f"Restored original matching mode: {self.original_matching_mode}")

            # Apply zone filtering logic before finalizing
            logger.info("Applying AK daily zones filtering logic")
            self.apply_ak_daily_zones_filtering()

            # Update button text to show completion
            self.ak_daily_vol_zones_button.setText("AK Daily Zones Active")
            self.ak_daily_vol_zones_button.setEnabled(True)

            # Update all tabs with AK daily zones data
            self.update_all_tabs_ak_daily_zones()

            # Log final completion
            hl_cached_graphs = [k for k in self.ak_daily_vol_zones_cache.keys() if 'volatility' in k and 'weekday' not in k]
            weekday_cached_graphs = [k for k in self.ak_daily_vol_zones_cache.keys() if 'weekday_volatility' in k]
            logger.info(f"AK's daily vol zones fully completed with filtering. H/L graphs: {len(hl_cached_graphs)}, Weekday graphs: {len(weekday_cached_graphs)}")

            # Debug: Log all cache contents after filtering
            for key, value in self.ak_daily_vol_zones_cache.items():
                if 'volatility' in key:
                    logger.info(f"Final filtered cache {key}: {len(value) if value else 0} levels")

        except Exception as e:
            logger.error(f"Error in finalize_ak_daily_zones: {str(e)}", exc_info=True)
            self.ak_daily_vol_zones_button.setText("Activate AK Daily Zones")
            self.ak_daily_vol_zones_button.setEnabled(True)

    def apply_ak_daily_zones_filtering(self):
        """
        Apply zone filtering logic to AK's daily vol zones data.

        Filtering Rules:
        1. 500DTL HL levels: Must be within ±0.0806% of any 200DTL/250DTL HL level, otherwise greyed out
        2. 500DTL weekday levels: Must have counterpart (same level type) in 200DTL/250DTL HL AND be within ±0.0806% of those counterparts, otherwise greyed out
        3. 200DTL/250DTL weekday levels: Must be within ±0.0806% of any 200DTL/250DTL HL level AND have counterpart (same level type) in 200DTL/250DTL HL, otherwise greyed out
        4. All minavg levels are completely excluded from plotting (not even greyed out)
        5. Max values (highest_high, lowest_low) and apex values are never deleted/filtered and always kept
        """
        try:
            logger.info("Starting AK daily zones filtering process")

            # Get all cached levels
            dtl_200_hl = self.ak_daily_vol_zones_cache.get('dtl_200_volatility', {})
            dtl_250_hl = self.ak_daily_vol_zones_cache.get('dtl_250_volatility', {})
            dtl_500_hl = self.ak_daily_vol_zones_cache.get('dtl_500_volatility', {})

            dtl_200_weekday = self.ak_daily_vol_zones_cache.get('dtl_200_weekday_volatility', {})
            dtl_250_weekday = self.ak_daily_vol_zones_cache.get('dtl_250_weekday_volatility', {})
            dtl_500_weekday = self.ak_daily_vol_zones_cache.get('dtl_500_weekday_volatility', {})

            # Define the tolerance percentage (0.0806%)
            tolerance_percent = 0.0806 / 100.0

            # Define level types for matching counterparts
            level_types = {
                'highest_high': 'max',
                'maxavg_high': 'maxavg',
                'avg_high_lowest_high': 'median',
                'minavg_high': 'minavg',
                'true_avg_high': 'avg',
                'apex': 'apex',
                'true_avg_low': 'avg',
                'minavg_low': 'minavg',
                'avg_low_highest_low': 'median',
                'maxavg_low': 'maxavg',
                'lowest_low': 'max'
            }

            # Define max values and apex that should never be filtered
            protected_level_keys = {'highest_high', 'lowest_low', 'apex'}

            def is_protected_value(level_key):
                """Check if a level is a protected value (max or apex) that should never be filtered"""
                return level_key in protected_level_keys

            # Count total reference levels for logging (before minavg filtering)
            total_200_levels = sum(1 for k, v in dtl_200_hl.items() if k in level_types and v is not None)
            total_250_levels = sum(1 for k, v in dtl_250_hl.items() if k in level_types and v is not None)

            logger.info(f"Reference HL levels (200DTL: {total_200_levels}, 250DTL: {total_250_levels}) before filtering")

            # First, completely exclude all minavg levels from all DTL sources
            excluded_minavg = []

            # Remove minavg levels from 200DTL HL
            dtl_200_hl_filtered = {}
            for level_key, level_value in dtl_200_hl.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"200DTL HL {level_key}={level_value:.4f}")
                else:
                    dtl_200_hl_filtered[level_key] = level_value

            # Remove minavg levels from 250DTL HL
            dtl_250_hl_filtered = {}
            for level_key, level_value in dtl_250_hl.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"250DTL HL {level_key}={level_value:.4f}")
                else:
                    dtl_250_hl_filtered[level_key] = level_value

            # Remove minavg levels from 500DTL HL
            dtl_500_hl_filtered = {}
            for level_key, level_value in dtl_500_hl.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"500DTL HL {level_key}={level_value:.4f}")
                else:
                    dtl_500_hl_filtered[level_key] = level_value

            # Remove minavg levels from weekday data
            dtl_200_weekday_filtered = {}
            for level_key, level_value in dtl_200_weekday.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"200DTL weekday {level_key}={level_value:.4f}")
                else:
                    dtl_200_weekday_filtered[level_key] = level_value

            dtl_250_weekday_filtered = {}
            for level_key, level_value in dtl_250_weekday.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"250DTL weekday {level_key}={level_value:.4f}")
                else:
                    dtl_250_weekday_filtered[level_key] = level_value

            dtl_500_weekday_filtered = {}
            for level_key, level_value in dtl_500_weekday.items():
                if 'minavg' in level_key:
                    excluded_minavg.append(f"500DTL weekday {level_key}={level_value:.4f}")
                else:
                    dtl_500_weekday_filtered[level_key] = level_value

            logger.info(f"Excluded minavg levels: {len(excluded_minavg)} - {excluded_minavg}")

            # Collect ALL reference levels from 200DTL and 250DTL (don't overwrite, collect all values)
            reference_hl_values = []
            for level_key, level_value in dtl_200_hl_filtered.items():
                if level_key in level_types and level_value is not None:
                    reference_hl_values.append(level_value)
            for level_key, level_value in dtl_250_hl_filtered.items():
                if level_key in level_types and level_value is not None:
                    reference_hl_values.append(level_value)

            logger.info(f"Reference HL values for tolerance checking: {len(reference_hl_values)} values (after minavg filtering)")

            # Filter 500DTL HL levels (mark as greyed out instead of removing)
            filtered_500_hl = {}
            greyed_500_hl = []

            for level_key, level_value in dtl_500_hl_filtered.items():
                # Skip non-level data (like high_values, low_values arrays)
                if level_key in ['high_values', 'low_values'] or level_value is None:
                    filtered_500_hl[level_key] = level_value
                    continue

                # Log unknown level keys for debugging
                if level_key not in level_types:
                    logger.warning(f"Unknown level key in 500DTL HL data: '{level_key}' = {level_value} - will be passed through without filtering")
                    filtered_500_hl[level_key] = level_value
                    continue

                # Protected values (max and apex) are never filtered - always keep them
                if is_protected_value(level_key):
                    filtered_500_hl[level_key] = level_value
                    continue

                # Check if this 500DTL level is within tolerance of any reference level
                within_tolerance = False
                for ref_value in reference_hl_values:
                    if ref_value is not None:
                        tolerance_range = abs(ref_value * tolerance_percent)
                        if abs(level_value - ref_value) <= tolerance_range:
                            within_tolerance = True
                            break

                if within_tolerance:
                    filtered_500_hl[level_key] = level_value
                else:
                    # Mark as greyed out instead of removing
                    filtered_500_hl[f"{level_key}_greyed"] = level_value
                    greyed_500_hl.append(f"{level_key}={level_value:.4f}")

            # Filter 500DTL weekday levels (mark as greyed out instead of removing)
            filtered_500_weekday = {}
            greyed_500_weekday = []

            for level_key, level_value in dtl_500_weekday_filtered.items():
                # Skip non-level data (like high_values, low_values arrays)
                if level_key in ['high_values', 'low_values'] or level_value is None:
                    filtered_500_weekday[level_key] = level_value
                    continue

                # Log unknown level keys for debugging
                if level_key not in level_types:
                    logger.warning(f"Unknown level key in 500DTL weekday data: '{level_key}' = {level_value} - will be passed through without filtering")
                    filtered_500_weekday[level_key] = level_value
                    continue

                # Protected values (max and apex) are never filtered - always keep them
                if is_protected_value(level_key):
                    filtered_500_weekday[level_key] = level_value
                    continue

                # 500DTL weekday levels need counterpart checking like other weekday levels
                # First check: Must have a counterpart (same level type) in 200DTL or 250DTL HL
                level_type = level_types[level_key]
                has_counterpart = False
                counterpart_values = []

                # Check in 200DTL HL (filtered)
                for ref_key in dtl_200_hl_filtered.keys():
                    if ref_key in level_types and level_types[ref_key] == level_type:
                        if dtl_200_hl_filtered[ref_key] is not None:
                            has_counterpart = True
                            counterpart_values.append(dtl_200_hl_filtered[ref_key])

                # Check in 250DTL HL (filtered)
                for ref_key in dtl_250_hl_filtered.keys():
                    if ref_key in level_types and level_types[ref_key] == level_type:
                        if dtl_250_hl_filtered[ref_key] is not None:
                            has_counterpart = True
                            counterpart_values.append(dtl_250_hl_filtered[ref_key])

                if not has_counterpart:
                    # No counterpart found - mark as greyed out
                    filtered_500_weekday[f"{level_key}_greyed"] = level_value
                    greyed_500_weekday.append(f"{level_key}={level_value:.4f} (no counterpart)")
                    continue

                # Second check: Must be within ±0.0806% of counterpart values
                within_tolerance = False
                for counterpart_value in counterpart_values:
                    tolerance_range = abs(counterpart_value * tolerance_percent)
                    if abs(level_value - counterpart_value) <= tolerance_range:
                        within_tolerance = True
                        break

                if within_tolerance:
                    filtered_500_weekday[level_key] = level_value
                else:
                    # Mark as greyed out instead of removing
                    filtered_500_weekday[f"{level_key}_greyed"] = level_value
                    greyed_500_weekday.append(f"{level_key}={level_value:.4f} (outside tolerance of counterparts)")

            # Filter weekday levels - must have counterpart in 200DTL or 250DTL HL AND be within tolerance
            def filter_weekday_levels(weekday_levels):
                filtered_weekday = {}
                greyed_weekday = []

                for level_key, level_value in weekday_levels.items():
                    # Skip non-level data (like high_values, low_values arrays)
                    if level_key in ['high_values', 'low_values'] or level_value is None:
                        filtered_weekday[level_key] = level_value
                        continue

                    # Log unknown level keys for debugging
                    if level_key not in level_types:
                        logger.warning(f"Unknown level key in weekday data: '{level_key}' = {level_value} - will be passed through without filtering")
                        filtered_weekday[level_key] = level_value
                        continue

                    # Protected values (max and apex) are never filtered - always keep them
                    if is_protected_value(level_key):
                        filtered_weekday[level_key] = level_value
                        continue

                    # First check: Must be within ±0.0806% of any 200DTL or 250DTL HL level
                    within_tolerance = False
                    for ref_value in reference_hl_values:
                        if ref_value is not None:
                            tolerance_range = abs(ref_value * tolerance_percent)
                            if abs(level_value - ref_value) <= tolerance_range:
                                within_tolerance = True
                                break

                    if not within_tolerance:
                        # Mark as greyed out instead of removing
                        filtered_weekday[f"{level_key}_greyed"] = level_value
                        greyed_weekday.append(f"{level_key}={level_value:.4f} (outside tolerance)")
                        continue

                    # Second check: Must have a counterpart (same level type) in 200DTL or 250DTL HL
                    level_type = level_types[level_key]
                    has_counterpart = False

                    # Check in 200DTL HL (filtered)
                    for ref_key in dtl_200_hl_filtered.keys():
                        if ref_key in level_types and level_types[ref_key] == level_type:
                            if dtl_200_hl_filtered[ref_key] is not None:
                                has_counterpart = True
                                break

                    # Check in 250DTL HL if not found in 200DTL (filtered)
                    if not has_counterpart:
                        for ref_key in dtl_250_hl_filtered.keys():
                            if ref_key in level_types and level_types[ref_key] == level_type:
                                if dtl_250_hl_filtered[ref_key] is not None:
                                    has_counterpart = True
                                    break

                    if has_counterpart:
                        filtered_weekday[level_key] = level_value
                    else:
                        # Mark as greyed out instead of removing
                        filtered_weekday[f"{level_key}_greyed"] = level_value
                        greyed_weekday.append(f"{level_key}={level_value:.4f} (no counterpart)")

                return filtered_weekday, greyed_weekday

            # Apply weekday filtering (using minavg-filtered data)
            filtered_200_weekday, greyed_200_weekday = filter_weekday_levels(dtl_200_weekday_filtered)
            filtered_250_weekday, greyed_250_weekday = filter_weekday_levels(dtl_250_weekday_filtered)

            # Update the cache with filtered data (now includes greyed out levels, excludes minavg completely)
            self.ak_daily_vol_zones_cache['dtl_200_volatility'] = dtl_200_hl_filtered
            self.ak_daily_vol_zones_cache['dtl_250_volatility'] = dtl_250_hl_filtered
            self.ak_daily_vol_zones_cache['dtl_500_volatility'] = filtered_500_hl
            self.ak_daily_vol_zones_cache['dtl_500_weekday_volatility'] = filtered_500_weekday
            self.ak_daily_vol_zones_cache['dtl_200_weekday_volatility'] = filtered_200_weekday
            self.ak_daily_vol_zones_cache['dtl_250_weekday_volatility'] = filtered_250_weekday

            # Log filtering results
            logger.info("AK Daily Zones Filtering Results:")
            logger.info(f"MinAvg levels completely excluded: {len(excluded_minavg)} - {excluded_minavg}")
            logger.info(f"500DTL HL levels greyed out: {len(greyed_500_hl)} - {greyed_500_hl}")
            logger.info(f"500DTL weekday levels greyed out: {len(greyed_500_weekday)} - {greyed_500_weekday}")
            logger.info(f"200DTL weekday levels greyed out: {len(greyed_200_weekday)} - {greyed_200_weekday}")
            logger.info(f"250DTL weekday levels greyed out: {len(greyed_250_weekday)} - {greyed_250_weekday}")

            total_greyed = len(greyed_500_hl) + len(greyed_500_weekday) + len(greyed_200_weekday) + len(greyed_250_weekday)
            total_excluded = len(excluded_minavg)
            logger.info(f"Total levels greyed out by filtering: {total_greyed}")
            logger.info(f"Total levels completely excluded: {total_excluded}")
            logger.info(f"Total levels affected by filtering: {total_greyed + total_excluded}")

            # Log apex preservation
            apex_count = 0
            for data_dict in [dtl_200_hl_filtered, dtl_250_hl_filtered, filtered_500_hl,
                             filtered_200_weekday, filtered_250_weekday, filtered_500_weekday]:
                if 'apex' in data_dict and data_dict['apex'] is not None:
                    apex_count += 1
            if apex_count > 0:
                logger.info(f"Apex values preserved as levels (not converted to zones): {apex_count} apex values found")

            # Clear any existing zone data to force fresh calculation
            if 'isolated_zones' in self.ak_daily_vol_zones_cache:
                del self.ak_daily_vol_zones_cache['isolated_zones']
            if 'extended_zones' in self.ak_daily_vol_zones_cache:
                del self.ak_daily_vol_zones_cache['extended_zones']
            logger.info("Cleared existing zone cache to force fresh calculation")

            # Create zones for ALL levels with proper mutual exclusion and 500DTL deletion
            # Note: Apex values are excluded from zone creation and remain as levels
            # Note: 500DTL levels cannot become isolated zones - they are deleted instead
            self.create_ak_daily_zones_with_exclusion(dtl_200_hl_filtered, dtl_250_hl_filtered, filtered_500_hl,
                                                     filtered_200_weekday, filtered_250_weekday, filtered_500_weekday,
                                                     tolerance_percent, level_types)

        except Exception as e:
            logger.error(f"Error in apply_ak_daily_zones_filtering: {str(e)}", exc_info=True)

    def create_ak_daily_zones_with_exclusion(self, dtl_200_hl, dtl_250_hl, dtl_500_hl,
                                            dtl_200_weekday, dtl_250_weekday, dtl_500_weekday,
                                            tolerance_percent, level_types):
        """
        Create zones for ALL levels (200/250/500 DTL, both HL and weekday) with proper mutual exclusion.
        Each level becomes either an isolated zone OR an extended zone, never both.
        Special rules:
        - Apex values are excluded from zone creation and always remain as levels
        - 500DTL levels cannot become isolated zones - they are deleted instead
        """
        try:
            logger.info("Creating AK daily zones for ALL levels with proper mutual exclusion")

            # Collect all levels from all sources for comprehensive checking (excluding apex values)
            all_levels = []

            # Add 200DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_200_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('200DTL_HL', level_key, level_value))

            # Add 250DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_250_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('250DTL_HL', level_key, level_value))

            # Add 500DTL HL levels (filtered, excluding apex values)
            for level_key, level_value in dtl_500_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('500DTL_HL', level_key, level_value))

            # Add 200DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_200_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('200DTL_WD', level_key, level_value))

            # Add 250DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_250_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('250DTL_WD', level_key, level_value))

            # Add 500DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_500_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('500DTL_WD', level_key, level_value))

            logger.info(f"Total levels for zone checking: {len(all_levels)}")

            # Zone extension percentage for isolated zones (0.033%)
            zone_extension_percent = 0.033 / 100.0

            # Process ALL levels to determine zone type (excluding apex values)
            isolated_zones = []
            extended_zones = []
            processed_levels = set()  # Track processed levels to avoid duplicates
            deleted_levels = []  # Track levels that were deleted (500DTL isolated cases)

            # Process 200DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_200_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"200DTL_HL_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('200DTL_HL', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted
                            deleted_levels.append(f"200DTL_HL {level_key}")
                        processed_levels.add(level_id)

            # Process 250DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_250_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"250DTL_HL_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('250DTL_HL', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted
                            deleted_levels.append(f"250DTL_HL {level_key}")
                        processed_levels.add(level_id)

            # Process 500DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_500_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"500DTL_HL_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('500DTL_HL', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted (500DTL isolated case)
                            deleted_levels.append(f"500DTL_HL {level_key}")
                        processed_levels.add(level_id)

            # Process 200DTL weekday levels (excluding apex values)
            for level_key, level_value in dtl_200_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"200DTL_WD_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('200DTL_WD', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted
                            deleted_levels.append(f"200DTL_WD {level_key}")
                        processed_levels.add(level_id)

            # Process 250DTL weekday levels (excluding apex values)
            for level_key, level_value in dtl_250_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"250DTL_WD_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('250DTL_WD', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted
                            deleted_levels.append(f"250DTL_WD {level_key}")
                        processed_levels.add(level_id)

            # Process 500DTL weekday levels (excluding apex values)
            for level_key, level_value in dtl_500_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    level_id = f"500DTL_WD_{level_key}_{level_value}"
                    if level_id not in processed_levels:
                        zone_info = self.determine_zone_type('500DTL_WD', level_key, level_value,
                                                           all_levels, tolerance_percent, level_types,
                                                           zone_extension_percent)
                        if zone_info:
                            if zone_info['zone_type'] == 'isolated':
                                isolated_zones.append(zone_info)
                            elif zone_info['zone_type'] == 'extended':
                                extended_zones.append(zone_info)
                        else:
                            # zone_info is None - level was deleted (500DTL isolated case)
                            deleted_levels.append(f"500DTL_WD {level_key}")
                        processed_levels.add(level_id)

            # Store zones in cache for plotting
            if isolated_zones:
                self.ak_daily_vol_zones_cache['isolated_zones'] = isolated_zones
                logger.info(f"Created {len(isolated_zones)} isolated level zones")
            else:
                logger.info("No isolated level zones created")

            if extended_zones:
                self.ak_daily_vol_zones_cache['extended_zones'] = extended_zones
                logger.info(f"Created {len(extended_zones)} extended counterpart zones")
            else:
                logger.info("No extended counterpart zones created")

            total_zones = len(isolated_zones) + len(extended_zones)
            total_processed = len(processed_levels)
            total_deleted = len(deleted_levels)
            logger.info(f"Processed {total_processed} unique levels, created {total_zones} zones (isolated: {len(isolated_zones)}, extended: {len(extended_zones)}), deleted {total_deleted} 500DTL isolated levels")

            if deleted_levels:
                logger.info(f"Deleted 500DTL levels that would have been isolated: {', '.join(deleted_levels)}")

        except Exception as e:
            logger.error(f"Error creating AK daily zones with exclusion: {str(e)}", exc_info=True)

    def determine_zone_type(self, source_dtl, level_key, level_value, all_levels, tolerance_percent, level_types, zone_extension_percent):
        """
        Determine whether a level should be an isolated zone or extended zone.
        Returns zone info with zone_type field indicating 'isolated' or 'extended'.
        Special rule: 500DTL levels cannot become isolated zones - they are deleted instead.
        """
        try:
            level_type = level_types[level_key]
            tolerance_range = abs(level_value * tolerance_percent)

            # Find all counterpart values within tolerance range (same level type)
            counterparts = []

            for other_source, other_key, other_value in all_levels:
                # Skip comparing with itself
                if source_dtl == other_source and level_key == other_key:
                    continue

                # Check if same level type and within tolerance
                if other_key in level_types and level_types[other_key] == level_type:
                    if abs(level_value - other_value) <= tolerance_range:
                        counterparts.append((other_source, other_key, other_value))

            # Decide zone type based on counterparts found
            if counterparts:
                # Has counterparts - create extended zone
                all_values = [level_value]
                counterpart_info = []

                for cp_source, cp_key, cp_value in counterparts:
                    all_values.append(cp_value)
                    counterpart_info.append({
                        'source': cp_source,
                        'key': cp_key,
                        'value': cp_value
                    })

                # Calculate zone bounds (min to max of all values)
                zone_lower = min(all_values)
                zone_upper = max(all_values)

                # Only create zone if there's meaningful extension (more than 0.001 difference)
                if abs(zone_upper - zone_lower) > 0.001:
                    zone_info = {
                        'zone_type': 'extended',
                        'source_dtl': source_dtl,
                        'level_key': level_key,
                        'level_value': level_value,
                        'zone_lower': zone_lower,
                        'zone_upper': zone_upper,
                        'zone_name': f"{source_dtl.replace('_', ' ')} {level_key.replace('_', ' ').title()} Extended Zone",
                        'counterparts': counterpart_info,
                        'counterpart_count': len(counterparts)
                    }

                    logger.info(f"Extended zone: {source_dtl} {level_key} = {level_value:.4f}, "
                              f"zone: {zone_lower:.4f} to {zone_upper:.4f}, "
                              f"includes {len(counterparts)} counterparts")

                    return zone_info
                else:
                    logger.info(f"Counterparts found for {source_dtl} {level_key} but zone extension too small, treating as isolated")

            # No counterparts or extension too small - would create isolated zone
            # Special rule: 500DTL levels cannot become isolated zones - DELETE them instead
            if '500DTL' in source_dtl:
                logger.info(f"500DTL level {source_dtl} {level_key} = {level_value:.4f} would be isolated - DELETING instead of creating zone")
                return None  # Return None to indicate deletion

            # For non-500DTL levels, create isolated zone
            zone_extension = abs(level_value * zone_extension_percent)
            zone_lower = level_value - zone_extension
            zone_upper = level_value + zone_extension

            zone_info = {
                'zone_type': 'isolated',
                'dtl_source': source_dtl.split('_')[0] + 'DTL',  # Convert '200DTL_HL' to '200DTL'
                'level_key': level_key,
                'level_value': level_value,
                'zone_lower': zone_lower,
                'zone_upper': zone_upper,
                'zone_name': f"{source_dtl.split('_')[0]}DTL {level_key.replace('_', ' ').title()} Zone"
            }

            logger.info(f"Isolated zone: {source_dtl} {level_key} = {level_value:.4f}, "
                      f"zone: {zone_lower:.4f} to {zone_upper:.4f}")

            return zone_info

        except Exception as e:
            logger.error(f"Error determining zone type for {source_dtl} {level_key}: {str(e)}", exc_info=True)
            return None

    def create_isolated_level_zones(self, dtl_200_hl, dtl_250_hl, tolerance_percent, level_types):
        """
        DEPRECATED: Use create_ak_daily_zones_with_exclusion instead.

        Create zones for 200DTL and 250DTL HL levels that don't have other levels within ±0.0806% range.
        Isolated levels get extended by ±0.033% into a zone with 30% transparency.
        Note: Apex values are excluded from zone creation and always remain as levels.
        """
        logger.warning("create_isolated_level_zones is deprecated. Use create_ak_daily_zones_with_exclusion instead.")
        try:
            logger.info("Creating zones for isolated 200DTL and 250DTL HL levels")

            # Combine all levels from 200DTL and 250DTL HL for checking isolation
            all_reference_levels = []

            # Collect all 200DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_200_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_reference_levels.append(('200DTL', level_key, level_value))

            # Collect all 250DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_250_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_reference_levels.append(('250DTL', level_key, level_value))

            logger.info(f"Total reference levels to check for isolation: {len(all_reference_levels)}")

            # Zone extension percentage (0.033%)
            zone_extension_percent = 0.033 / 100.0

            # Check each level for isolation
            isolated_zones = []

            for dtl_source, level_key, level_value in all_reference_levels:
                # Check if this level has any other levels within ±0.0806% range
                has_nearby_levels = False

                for other_dtl, other_key, other_value in all_reference_levels:
                    # Skip comparing with itself
                    if dtl_source == other_dtl and level_key == other_key:
                        continue

                    # Calculate tolerance range for this level
                    tolerance_range = abs(level_value * tolerance_percent)

                    # Check if other level is within tolerance range
                    if abs(level_value - other_value) <= tolerance_range:
                        has_nearby_levels = True
                        break

                # If no nearby levels found, this level is isolated
                if not has_nearby_levels:
                    # Calculate zone bounds (±0.033%)
                    zone_extension = abs(level_value * zone_extension_percent)
                    zone_lower = level_value - zone_extension
                    zone_upper = level_value + zone_extension

                    isolated_zones.append({
                        'dtl_source': dtl_source,
                        'level_key': level_key,
                        'level_value': level_value,
                        'zone_lower': zone_lower,
                        'zone_upper': zone_upper,
                        'zone_name': f"{dtl_source} {level_key.replace('_', ' ').title()} Zone"
                    })

                    logger.info(f"Isolated level found: {dtl_source} {level_key} = {level_value:.4f}, zone: {zone_lower:.4f} to {zone_upper:.4f}")

            # Store isolated zones in cache for plotting
            if isolated_zones:
                self.ak_daily_vol_zones_cache['isolated_zones'] = isolated_zones
                logger.info(f"Created {len(isolated_zones)} isolated level zones")
            else:
                logger.info("No isolated levels found - no zones created")

        except Exception as e:
            logger.error(f"Error creating isolated level zones: {str(e)}", exc_info=True)

    def create_counterpart_extended_zones(self, dtl_200_hl, dtl_250_hl, dtl_500_hl,
                                        dtl_200_weekday, dtl_250_weekday, dtl_500_weekday,
                                        tolerance_percent, level_types):
        """
        DEPRECATED: Use create_ak_daily_zones_with_exclusion instead.

        Create extended zones for 200DTL and 250DTL HL levels that have counterpart values within ±0.0806% range.
        If counterpart values (same level type) are within range, extend the zone to include those values.
        Note: Apex values are excluded from zone creation and always remain as levels.
        """
        logger.warning("create_counterpart_extended_zones is deprecated. Use create_ak_daily_zones_with_exclusion instead.")
        try:
            logger.info("Creating extended zones for 200DTL and 250DTL HL levels with counterparts")

            # Collect all levels from all sources for counterpart checking (excluding apex values)
            all_levels = []

            # Add 200DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_200_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('200DTL_HL', level_key, level_value))

            # Add 250DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_250_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('250DTL_HL', level_key, level_value))

            # Add 500DTL HL levels (filtered, excluding apex values)
            for level_key, level_value in dtl_500_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('500DTL_HL', level_key, level_value))

            # Add 200DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_200_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('200DTL_WD', level_key, level_value))

            # Add 250DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_250_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('250DTL_WD', level_key, level_value))

            # Add 500DTL weekday levels (filtered, excluding apex values)
            for level_key, level_value in dtl_500_weekday.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    all_levels.append(('500DTL_WD', level_key, level_value))

            logger.info(f"Total levels for counterpart zone checking: {len(all_levels)}")

            # Check each 200DTL and 250DTL HL level for counterparts (excluding apex values)
            extended_zones = []

            # Process 200DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_200_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    zone_info = self.find_counterpart_zone('200DTL_HL', level_key, level_value,
                                                         all_levels, tolerance_percent, level_types)
                    if zone_info:
                        extended_zones.append(zone_info)

            # Process 250DTL HL levels (excluding apex values)
            for level_key, level_value in dtl_250_hl.items():
                if level_key in level_types and level_value is not None and level_key != 'apex':
                    zone_info = self.find_counterpart_zone('250DTL_HL', level_key, level_value,
                                                         all_levels, tolerance_percent, level_types)
                    if zone_info:
                        extended_zones.append(zone_info)

            # Store extended zones in cache for plotting
            if extended_zones:
                self.ak_daily_vol_zones_cache['extended_zones'] = extended_zones
                logger.info(f"Created {len(extended_zones)} extended counterpart zones")
            else:
                logger.info("No extended counterpart zones created")

        except Exception as e:
            logger.error(f"Error creating counterpart extended zones: {str(e)}", exc_info=True)

    def find_counterpart_zone(self, source_dtl, level_key, level_value, all_levels, tolerance_percent, level_types):
        """
        Find counterpart values (same level type) within tolerance range and create extended zone.
        """
        try:
            level_type = level_types[level_key]
            tolerance_range = abs(level_value * tolerance_percent)

            # Find all counterpart values within tolerance range
            counterparts = []

            for other_source, other_key, other_value in all_levels:
                # Skip comparing with itself
                if source_dtl == other_source and level_key == other_key:
                    continue

                # Check if same level type and within tolerance
                if other_key in level_types and level_types[other_key] == level_type:
                    if abs(level_value - other_value) <= tolerance_range:
                        counterparts.append((other_source, other_key, other_value))

            # If counterparts found, create extended zone
            if counterparts:
                # Collect all values (original + counterparts)
                all_values = [level_value]
                counterpart_info = []

                for cp_source, cp_key, cp_value in counterparts:
                    all_values.append(cp_value)
                    counterpart_info.append(f"{cp_source} {cp_key} ({cp_value:.4f})")

                # Calculate zone bounds (min to max of all values)
                zone_lower = min(all_values)
                zone_upper = max(all_values)

                # Only create zone if there's meaningful extension (more than 0.001 difference)
                if abs(zone_upper - zone_lower) > 0.001:
                    zone_info = {
                        'source_dtl': source_dtl,
                        'level_key': level_key,
                        'level_value': level_value,
                        'zone_lower': zone_lower,
                        'zone_upper': zone_upper,
                        'zone_name': f"{source_dtl.replace('_', ' ')} {level_key.replace('_', ' ').title()} Extended Zone",
                        'counterparts': counterpart_info,
                        'counterpart_count': len(counterparts)
                    }

                    logger.info(f"Extended zone: {source_dtl} {level_key} = {level_value:.4f}, "
                              f"zone: {zone_lower:.4f} to {zone_upper:.4f}, "
                              f"includes {len(counterparts)} counterparts")

                    return zone_info

            return None

        except Exception as e:
            logger.error(f"Error finding counterpart zone for {source_dtl} {level_key}: {str(e)}", exc_info=True)
            return None

    def debug_ak_daily_cache(self):
        """Debug method to show AK daily vol zones cache contents"""
        try:
            from PyQt6 import QtWidgets

            # Create debug message
            debug_info = []
            debug_info.append(f"AK Daily Vol Zones Active: {self.ak_daily_vol_zones_active}")
            debug_info.append(f"Cache Keys: {list(self.ak_daily_vol_zones_cache.keys())}")
            debug_info.append(f"Data Keys: {list(self.ak_daily_vol_zones_data.keys())}")
            debug_info.append("")

            # Separate H/L and weekday caches for better display
            hl_caches = {k: v for k, v in self.ak_daily_vol_zones_cache.items() if 'volatility' in k and 'weekday' not in k}
            weekday_caches = {k: v for k, v in self.ak_daily_vol_zones_cache.items() if 'weekday_volatility' in k}

            debug_info.append("H/L Matching Caches:")
            for key, value in hl_caches.items():
                debug_info.append(f"  {key}:")
                if value:
                    debug_info.append(f"    Levels count: {len(value)}")
                    level_summary = {k: v for k, v in value.items() if k not in ['high_values', 'low_values'] and v is not None}
                    debug_info.append(f"    Available levels: {list(level_summary.keys())}")
                    debug_info.append(f"    Sample values: {dict(list(level_summary.items())[:3])}")
                else:
                    debug_info.append("    No levels cached")
                debug_info.append("")

            debug_info.append("Weekday Matching Caches:")
            for key, value in weekday_caches.items():
                debug_info.append(f"  {key}:")
                if value:
                    debug_info.append(f"    Levels count: {len(value)}")
                    level_summary = {k: v for k, v in value.items() if k not in ['high_values', 'low_values'] and v is not None}
                    debug_info.append(f"    Available levels: {list(level_summary.keys())}")
                    debug_info.append(f"    Sample values: {dict(list(level_summary.items())[:3])}")
                else:
                    debug_info.append("    No levels cached")
                debug_info.append("")

            # Show debug dialog
            QtWidgets.QMessageBox.information(self, "AK Daily Vol Zones Debug", "\n".join(debug_info))

        except Exception as e:
            logger.error(f"Error in debug_ak_daily_cache: {str(e)}", exc_info=True)

    def ak_odds_zones(self):
        """
        AK's odds zones functionality.
        Caches and stores every theoretical intersect from 200DTL, 250DTL, and 500DTL
        for both H/L matching and weekday matching modes.
        """
        try:
            # Toggle the AK odds zones state
            self.ak_odds_zones_active = not self.ak_odds_zones_active

            if self.ak_odds_zones_active:
                logger.info("Activating AK's odds zones - loading theoretical intersects for 200, 250, 500 DTL")

                # Update button text to show loading state
                self.ak_odds_zones_button.setText("Loading Intersects...")
                self.ak_odds_zones_button.setEnabled(False)

                # Clear any existing cached data
                self.ak_odds_zones_data.clear()
                self.ak_odds_zones_cache.clear()

                # Start the consecutive intersect loading process
                self.load_consecutive_intersect_data()
            else:
                logger.info("Deactivating AK's odds zones")

                # Clear cached data
                self.ak_odds_zones_data.clear()
                self.ak_odds_zones_cache.clear()

                # Update button text
                self.ak_odds_zones_button.setText("Activate AK Odds Zones")
                self.ak_odds_zones_button.setEnabled(True)

                # Update all tabs to remove AK odds zones
                self.update_all_tabs_ak_odds_zones()

        except Exception as e:
            logger.error(f"Error in ak_odds_zones: {str(e)}", exc_info=True)
            # Reset button state on error
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def ak_density_zones(self):
        """
        AK's density zones functionality.
        Toggle for density intersections display.
        When activated, shows a popup to get EPS values for peak and trough clustering.
        """
        try:
            # If currently active, just deactivate
            if self.ak_density_zones_active:
                logger.info("Deactivating AK's density zones - disabling density intersections")
                self.ak_density_zones_active = False
                # Clear cached data
                self.ak_density_zones_cache.clear()
                # Update button text
                self.ak_density_zones_button.setText("AK's Users Choice Density Zones")
                # Hide save zones button
                self.save_zones_to_cache_button.setVisible(False)
                # Update all tabs to show/hide AK density zones
                self.update_all_tabs_ak_density_zones()
                return

            # If not active, show EPS input dialog
            eps_dialog = self.show_eps_input_dialog()
            if eps_dialog is None:
                # User cancelled or didn't provide input
                return

            eps_value, _ = eps_dialog  # Both values are the same

            # Activate AK's density zones with the provided EPS value
            logger.info(f"Activating AK's density zones with EPS: {eps_value}")
            self.ak_density_zones_active = True

            # Update button text
            self.ak_density_zones_button.setText("AK's Users Choice Density Zones Active")

            # Set the EPS value in the density graph tab
            if hasattr(self, 'density_graph_tab') and self.density_graph_tab:
                self.density_graph_tab.peaks_troughs_eps = eps_value
                logger.info(f"Set peaks_troughs_eps to {eps_value} in density graph tab")

                # Automatically enable density profile and disable options for optimal view
                # Turn on density profile (volume profile)
                self.density_graph_tab.show_volume_profile = True
                self.density_graph_tab.show_volume_profile_btn.setChecked(True)

                # Turn off options data
                self.density_graph_tab.show_option_data = False
                self.density_graph_tab.show_option_data_btn.setChecked(False)

                # Turn off options zones (Eth's Option Zones)
                self.density_graph_tab.show_eth_zones = False
                self.density_graph_tab.show_eth_zones_btn.setChecked(False)

                logger.info("Auto-enabled density profile and disabled options for optimal AK density zones view")

            # Update all tabs to show/hide AK density zones
            self.update_all_tabs_ak_density_zones()

            # Update save zones button visibility
            self.update_save_zones_button_visibility()

        except Exception as e:
            logger.error(f"Error in ak_density_zones: {str(e)}", exc_info=True)
            # Reset button state on error
            self.ak_density_zones_button.setText("AK's Users Choice Density Zones")
            # Hide save zones button on error
            self.save_zones_to_cache_button.setVisible(False)

    def show_eps_input_dialog(self):
        """
        Show a dialog to get EPS value for peak and trough clustering.
        Note: Both peaks and troughs use the same EPS parameter in the clustering algorithm.

        Returns:
            tuple: (eps_value, eps_value) if user provides valid input, None if cancelled
        """
        try:
            # Create the dialog
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("EPS Configuration for Density Zones")
            dialog.setModal(True)
            dialog.setFixedSize(450, 280)

            # Apply theme styling
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {THEME_COLORS['control_panel']};
                    color: {THEME_COLORS['text']};
                }}
                QLabel {{
                    color: {THEME_COLORS['text']};
                    font-size: 12px;
                    font-weight: bold;
                }}
                QLineEdit {{
                    background-color: {THEME_COLORS['background']};
                    color: {THEME_COLORS['text']};
                    border: 1px solid {THEME_COLORS['borders']};
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 11px;
                }}
                QPushButton {{
                    background-color: {THEME_COLORS['primary_accent']};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 11px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {THEME_COLORS['secondary_accent']};
                }}
                QPushButton:pressed {{
                    background-color: {THEME_COLORS['pressed_accent']};
                }}
            """)

            # Create layout
            layout = QtWidgets.QVBoxLayout(dialog)
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # Title label
            title_label = QtWidgets.QLabel("Configure EPS value for peak and trough clustering:")
            layout.addWidget(title_label)

            # EPS input
            eps_layout = QtWidgets.QHBoxLayout()
            eps_label = QtWidgets.QLabel("EPS Value:")
            eps_label.setMinimumWidth(80)
            self.eps_input = QtWidgets.QLineEdit("0.5")  # Default value
            eps_layout.addWidget(eps_label)
            eps_layout.addWidget(self.eps_input)
            layout.addLayout(eps_layout)

            # Add some spacing
            layout.addSpacing(10)

            # Template buttons
            template_label = QtWidgets.QLabel("Quick Templates:")
            template_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
            layout.addWidget(template_label)

            template_layout = QtWidgets.QHBoxLayout()
            template_layout.setSpacing(10)

            # MES template button
            es_template_btn = QtWidgets.QPushButton("MES (10.0)")
            es_template_btn.setToolTip("Micro E-mini S&P 500 futures - Recommended EPS: 10.0 for looser clustering")
            es_template_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {THEME_COLORS['control_panel']};
                    color: {THEME_COLORS['text']};
                    border: 1px solid {THEME_COLORS['borders']};
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 10px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {THEME_COLORS['primary_accent']};
                    color: white;
                }}
                QPushButton:pressed {{
                    background-color: {THEME_COLORS['pressed_accent']};
                }}
            """)
            es_template_btn.clicked.connect(lambda: self.eps_input.setText("10.0"))
            template_layout.addWidget(es_template_btn)

            # SPY/QQQ template button
            spy_template_btn = QtWidgets.QPushButton("SPY/QQQ (0.5)")
            spy_template_btn.setToolTip("SPY & QQQ ETFs - Recommended EPS: 0.5 for tighter clustering")
            spy_template_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {THEME_COLORS['control_panel']};
                    color: {THEME_COLORS['text']};
                    border: 1px solid {THEME_COLORS['borders']};
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 10px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {THEME_COLORS['primary_accent']};
                    color: white;
                }}
                QPushButton:pressed {{
                    background-color: {THEME_COLORS['pressed_accent']};
                }}
            """)
            spy_template_btn.clicked.connect(lambda: self.eps_input.setText("0.5"))
            template_layout.addWidget(spy_template_btn)

            template_layout.addStretch()  # Push buttons to the left
            layout.addLayout(template_layout)

            # Add some spacing
            layout.addSpacing(5)

            # Info label
            info_label = QtWidgets.QLabel("(Lower values = tighter clustering, Higher values = looser clustering)")
            info_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 10px; font-style: italic;")
            layout.addWidget(info_label)

            # Button layout
            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addStretch()

            # OK button
            ok_button = QtWidgets.QPushButton("OK")
            ok_button.clicked.connect(dialog.accept)
            button_layout.addWidget(ok_button)

            # Cancel button
            cancel_button = QtWidgets.QPushButton("Cancel")
            cancel_button.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_button)

            layout.addLayout(button_layout)

            # Show dialog and get result
            result = dialog.exec()

            if result == QtWidgets.QDialog.DialogCode.Accepted:
                try:
                    # Validate and parse input
                    eps_text = self.eps_input.text().strip()

                    if not eps_text:
                        QtWidgets.QMessageBox.warning(self, "Invalid Input",
                                                    "Please enter an EPS value.")
                        return None

                    eps_value = float(eps_text)

                    if eps_value <= 0:
                        QtWidgets.QMessageBox.warning(self, "Invalid Input",
                                                    "EPS value must be a positive number.")
                        return None

                    logger.info(f"User provided EPS value: {eps_value}")
                    # Return the same value twice for compatibility with existing code
                    return (eps_value, eps_value)

                except ValueError:
                    QtWidgets.QMessageBox.warning(self, "Invalid Input",
                                                "Please enter a valid numeric value for EPS.")
                    return None
            else:
                logger.info("User cancelled EPS input dialog")
                return None

        except Exception as e:
            logger.error(f"Error showing EPS input dialog: {str(e)}", exc_info=True)
            return None

    def update_all_tabs_ak_density_zones(self):
        """Update all tabs to show/hide AK density zones data"""
        try:
            # Update density graph tab
            if hasattr(self.density_graph_tab, 'ak_density_zones_active'):
                self.density_graph_tab.ak_density_zones_active = self.ak_density_zones_active

                # Trigger density graph regeneration to apply the change
                self.density_graph_tab.generate_density_graph()

        except Exception as e:
            logger.error(f"Error updating tabs with AK density zones: {str(e)}", exc_info=True)

    def update_save_zones_button_visibility(self):
        """Update the visibility of the save zones to cache button based on AK density zones state"""
        try:
            # Show button only when AK density zones are active and zones exist
            should_show = (self.ak_density_zones_active and
                          hasattr(self, 'density_graph_tab') and
                          self.density_graph_tab and
                          hasattr(self.density_graph_tab, 'ak_density_zones') and
                          len(self.density_graph_tab.ak_density_zones) > 0)

            self.save_zones_to_cache_button.setVisible(should_show)

            if should_show:
                logger.info(f"Save zones button shown - {len(self.density_graph_tab.ak_density_zones)} zones available")
            else:
                logger.info("Save zones button hidden - no zones available or AK density zones inactive")

        except Exception as e:
            logger.error(f"Error updating save zones button visibility: {str(e)}", exc_info=True)
            self.save_zones_to_cache_button.setVisible(False)

    def save_zones_to_cache(self):
        """Save density zones to cache for later use by the candlestick chart"""
        try:
            logger.info("Save zones to cache button clicked")

            # Check if AK density zones are active
            if not self.ak_density_zones_active:
                QtWidgets.QMessageBox.warning(self, "Save Zones",
                    "AK's density zones are not active.\nPlease activate them first.")
                return

            # Check if density graph tab exists
            if not hasattr(self, 'density_graph_tab') or not self.density_graph_tab:
                QtWidgets.QMessageBox.warning(self, "Save Zones",
                    "Density graph tab not found.")
                return

            # Check if zones exist
            if not hasattr(self.density_graph_tab, 'ak_density_zones') or not self.density_graph_tab.ak_density_zones:
                QtWidgets.QMessageBox.warning(self, "Save Zones",
                    "No density zones found to save.\nPlease generate AK's density zones first.")
                return

            # Clear existing cache
            self.ak_density_zones_cache.clear()

            # Convert density zones to cache format
            cached_zones = []
            for zone in self.density_graph_tab.ak_density_zones:
                try:
                    # Extract zone data in a format similar to other AK zone caches
                    zone_data = {
                        'zone_id': zone.zone_id,
                        'name': f"{zone.area_name} ({zone.zone_type})",
                        'top': zone.zone_bounds['max_y'],
                        'bottom': zone.zone_bounds['min_y'],
                        'color': zone.zone_color,
                        'zone_type': zone.zone_type,
                        'area_name': zone.area_name,
                        'primary_element': zone.primary_element,
                        'combining_elements': zone.combining_elements or [],
                        'description': f"AK Density Zone - {zone.area_name}"
                    }
                    cached_zones.append(zone_data)
                    logger.debug(f"Cached zone {zone.zone_id}: {zone.area_name} ({zone.zone_bounds['min_y']:.2f} to {zone.zone_bounds['max_y']:.2f})")

                except Exception as e:
                    logger.error(f"Error processing zone {zone.zone_id}: {str(e)}")
                    continue

            # Store zones in cache
            self.ak_density_zones_cache['zones'] = cached_zones

            # Show success message
            zones_count = len(cached_zones)
            QtWidgets.QMessageBox.information(self, "Save Zones",
                f"Successfully saved {zones_count} density zones to cache.\n\nUse 'Plot AK Density Zones' button on the candlestick chart to display them.")

            logger.info(f"Successfully cached {zones_count} density zones. Cache keys: {list(self.ak_density_zones_cache.keys())}")

        except Exception as e:
            logger.error(f"Error saving zones to cache: {str(e)}", exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Save Zones Error",
                f"Error saving zones to cache:\n{str(e)}")



    def on_density_200dtl_clicked(self):
        """Handle Length 1 Daily 200DTL density preset button click."""
        # Show EPS input dialog first
        eps_dialog = self.show_eps_input_dialog()
        if eps_dialog is None:
            return  # User cancelled
        eps_value, _ = eps_dialog  # Both values are the same
        self.apply_density_preset(vector_length=1, timeframe='1d', days_to_load=200,
                                preset_name="L1 Daily 200DTL", eps_value=eps_value)

    def on_density_500dtl_clicked(self):
        """Handle Length 1 Daily 500DTL density preset button click."""
        # Show EPS input dialog first
        eps_dialog = self.show_eps_input_dialog()
        if eps_dialog is None:
            return  # User cancelled
        eps_value, _ = eps_dialog  # Both values are the same
        self.apply_density_preset(vector_length=1, timeframe='1d', days_to_load=500,
                                preset_name="L1 Daily 500DTL", eps_value=eps_value)

    def on_density_15k_clicked(self):
        """Handle Length 3 15k DTL density preset button click."""
        # Show EPS input dialog first
        eps_dialog = self.show_eps_input_dialog()
        if eps_dialog is None:
            return  # User cancelled
        eps_value, _ = eps_dialog  # Both values are the same
        self.apply_density_preset(vector_length=3, timeframe='1d', days_to_load=15000,
                                preset_name="L3 15k DTL", eps_value=eps_value)

    def apply_density_preset(self, vector_length, timeframe, days_to_load, preset_name, eps_value=0.5):
        """
        Apply a density preset configuration and switch to density graph with zones active.

        Args:
            vector_length: Vector length to set
            timeframe: Timeframe to set (e.g., '1d')
            days_to_load: Number of days to load
            preset_name: Name of the preset for logging
            eps_value: EPS value for peak and trough clustering (default: 0.5)
        """
        try:
            logger.info(f"Applying density preset: {preset_name} (VL={vector_length}, TF={timeframe}, DTL={days_to_load})")

            # Import parameter registry
            from parameter_registry import default_registry

            # Set the parameters in the registry
            default_registry.set_value('vector_length', vector_length)
            default_registry.set_value('timeframe', timeframe)
            default_registry.set_value('days_to_load', days_to_load)

            # Activate AK's density zones if not already active
            if not self.ak_density_zones_active:
                logger.info("Activating AK's density zones for preset")
                self.ak_density_zones_active = True
                # Update the button text
                self.ak_density_zones_button.setText("AK's Users Choice Density Zones Active")
                # Update all tabs to show AK density zones
                self.update_all_tabs_ak_density_zones()

            # Set the EPS value in the density graph tab
            if hasattr(self, 'density_graph_tab') and self.density_graph_tab:
                self.density_graph_tab.peaks_troughs_eps = eps_value
                logger.info(f"Set peaks_troughs_eps to {eps_value} for preset {preset_name}")

            # Switch to density graph tab
            self.density_graph_btn.setChecked(True)
            self.stacked_widget.setCurrentIndex(1)
            logger.info("Switched to density graph tab")

            # Trigger data fetch with new parameters
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()

            # Get current symbol
            current_symbol = default_registry.get_value('symbol')
            if current_symbol and current_symbol.strip():
                logger.info(f"Starting data fetch for {preset_name}: {current_symbol}, {timeframe}, {days_to_load} days")

                # Update button text to show loading
                button_map = {
                    "L1 Daily 200DTL": self.density_200dtl_btn,
                    "L1 Daily 500DTL": self.density_500dtl_btn,
                    "L3 15k DTL": self.density_15k_btn
                }
                if preset_name in button_map:
                    button_map[preset_name].setText(f"Loading {preset_name}...")
                    button_map[preset_name].setEnabled(False)

                # Clean up any existing threads
                dispatcher.cleanup_all_threads()
                # Clear cache to force fresh data fetch
                dispatcher.clear_cache()

                # Connect to data ready signal
                dispatcher.data_fetched.connect(self.on_preset_data_ready)

                # Fetch data with new parameters
                dispatcher.fetch_data(current_symbol, timeframe, days_to_load)
                logger.info(f"Data fetch initiated for {preset_name}: {current_symbol}, {timeframe}, {days_to_load} days")

                # Update status in density graph tab
                if hasattr(self.density_graph_tab, 'status_label'):
                    self.density_graph_tab.status_label.setText(f"Loading {preset_name} data for {current_symbol}...")
            else:
                logger.warning("No symbol available for data fetch")
                if hasattr(self.density_graph_tab, 'status_label'):
                    self.density_graph_tab.status_label.setText(f"Applied {preset_name} preset - no symbol to fetch")

        except Exception as e:
            logger.error(f"Error applying density preset {preset_name}: {str(e)}", exc_info=True)
            if hasattr(self.density_graph_tab, 'status_label'):
                self.density_graph_tab.status_label.setText(f"Error applying {preset_name} preset")

    def on_preset_data_ready(self, symbol, data):
        """Handle data ready after applying a density preset."""
        try:
            logger.info(f"Data ready for density preset: {symbol} - {len(data)} rows loaded")
            # Disconnect the signal to avoid multiple connections
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.data_fetched.disconnect(self.on_preset_data_ready)

            # Restore button states
            self.density_200dtl_btn.setText("L1 Daily 200DTL")
            self.density_200dtl_btn.setEnabled(True)
            self.density_500dtl_btn.setText("L1 Daily 500DTL")
            self.density_500dtl_btn.setEnabled(True)
            self.density_15k_btn.setText("L3 15k DTL")
            self.density_15k_btn.setEnabled(True)

            # Update status and regenerate graph in density tab
            if hasattr(self.density_graph_tab, 'status_label'):
                self.density_graph_tab.status_label.setText(f"Data loaded for {symbol} - generating density graph with zones")

            # Regenerate the density graph with new data
            if hasattr(self.density_graph_tab, 'generate_density_graph'):
                logger.info("Triggering density graph regeneration with new data")
                self.density_graph_tab.generate_density_graph()
            else:
                logger.warning("Density graph tab does not have generate_density_graph method")

        except Exception as e:
            logger.error(f"Error handling preset data ready: {str(e)}", exc_info=True)
            # Restore button states on error
            self.density_200dtl_btn.setText("L1 Daily 200DTL")
            self.density_200dtl_btn.setEnabled(True)
            self.density_500dtl_btn.setText("L1 Daily 500DTL")
            self.density_500dtl_btn.setEnabled(True)
            self.density_15k_btn.setText("L3 15k DTL")
            self.density_15k_btn.setEnabled(True)



    def load_consecutive_intersect_data(self):
        """
        Load data consecutively for 200 DTL, 250 DTL, and 500 DTL to cache theoretical intersects.
        """
        try:
            # Get current parameters
            from parameter_registry import default_registry
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')

            if not symbol or not timeframe:
                logger.error("Symbol or timeframe not available for intersect data loading")
                self.ak_odds_zones_button.setText("Activate AK Odds Zones")
                self.ak_odds_zones_button.setEnabled(True)
                return

            # Store original DTL and matching mode
            self.original_dtl = default_registry.get_value('days_to_load')
            self.original_matching_mode = 'hl' if self.hl_matching_btn.isChecked() else 'weekday'

            # DTL values to load consecutively
            self.intersect_dtl_values = [200, 250, 500]
            self.current_intersect_dtl_index = 0
            self.intersect_loaded_data = {}

            # Start with H/L matching mode
            if not self.hl_matching_btn.isChecked():
                logger.info("Switching to H/L matching mode for intersect loading")
                self.hl_matching_btn.setChecked(True)

            # Start loading the first DTL value
            self.load_next_intersect_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in load_consecutive_intersect_data: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def load_next_intersect_dtl_data(self, symbol, timeframe):
        """
        Load data for the next DTL value in the intersect sequence.
        """
        try:
            if self.current_intersect_dtl_index >= len(self.intersect_dtl_values):
                # All H/L matching DTL values loaded, now switch to weekday matching
                self.start_weekday_intersect_processing()
                return

            dtl_value = self.intersect_dtl_values[self.current_intersect_dtl_index]
            logger.info(f"Loading intersect DTL {dtl_value} data ({self.current_intersect_dtl_index + 1}/{len(self.intersect_dtl_values)})")

            # Update button text to show progress
            self.ak_odds_zones_button.setText(f"Loading {dtl_value} DTL Intersects ({self.current_intersect_dtl_index + 1}/{len(self.intersect_dtl_values)})...")

            # Update the parameter registry with the new DTL value
            from parameter_registry import default_registry
            default_registry.set_value('days_to_load', dtl_value)

            # Get data dispatcher instance
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()

            # Connect to data ready signal for this specific request
            dispatcher.data_fetched.connect(self.on_intersect_dtl_data_ready)

            # Fetch data for this DTL value
            dispatcher.fetch_data(symbol, timeframe, dtl_value)

        except Exception as e:
            logger.error(f"Error in load_next_intersect_dtl_data: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def on_intersect_dtl_data_ready(self, symbol, data):
        """
        Handle data ready signal for intersect DTL loading.
        """
        try:
            # Disconnect the signal to avoid multiple calls
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.data_fetched.disconnect(self.on_intersect_dtl_data_ready)

            dtl_value = self.intersect_dtl_values[self.current_intersect_dtl_index]
            logger.info(f"Data ready for intersect DTL {dtl_value}: {len(data) if data is not None else 0} rows")

            # Store the data
            self.intersect_loaded_data[dtl_value] = data.copy() if data is not None else None

            # Cache theoretical intersects for this DTL value
            self.cache_theoretical_intersects_for_dtl(dtl_value, data)

            # Move to next DTL value
            self.current_intersect_dtl_index += 1

            # Load next DTL value or proceed to weekday matching
            from parameter_registry import default_registry
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')
            self.load_next_intersect_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in on_intersect_dtl_data_ready: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def cache_theoretical_intersects_for_dtl(self, dtl_value, data):
        """
        Cache theoretical intersects that the FWL Odds tab generates for the specified DTL value.
        """
        try:
            if data is None or data.empty:
                logger.warning(f"No data available for DTL {dtl_value} intersect caching")
                return

            logger.info(f"Caching theoretical intersects for DTL {dtl_value}")

            # Temporarily change DTL to this value and trigger FWL Odds generation
            from parameter_registry import default_registry
            original_dtl = default_registry.get_value('days_to_load')

            # Set the DTL value
            default_registry.set_value('days_to_load', dtl_value)

            # Trigger FWL Odds generation to get the intersects
            self.fwl_odds_tab.generate_fwl_odds()

            # Get the theoretical intersects from the FWL Odds tab
            # The intersects are calculated in the find_line_intersections method
            theoretical_intersects = self.extract_theoretical_intersects_from_fwl_odds()

            # Restore original DTL
            default_registry.set_value('days_to_load', original_dtl)

            # Determine the matching mode for cache key
            matching_mode = 'hl' if self.hl_matching_btn.isChecked() else 'weekday'

            # Cache the theoretical intersects
            cache_key = f"dtl_{dtl_value}_{matching_mode}_intersects"
            self.ak_odds_zones_cache[cache_key] = theoretical_intersects.copy() if theoretical_intersects else []

            # Debug: Log the cached intersects
            if theoretical_intersects:
                logger.info(f"DTL {dtl_value} {matching_mode} cached {len(theoretical_intersects)} intersects")
                for i, intersect in enumerate(theoretical_intersects[:5]):  # Log first 5
                    logger.info(f"  Intersect {i+1}: y={intersect:.4f}")
            else:
                logger.info(f"DTL {dtl_value} {matching_mode}: No intersects found")

        except Exception as e:
            logger.error(f"Error caching theoretical intersects for DTL {dtl_value}: {str(e)}", exc_info=True)

    def extract_theoretical_intersects_from_fwl_odds(self):
        """
        Extract theoretical intersects from the FWL Odds tab after it has generated the graph.
        This method accesses the intersections that were calculated and displayed.
        """
        try:
            theoretical_intersects = []

            # Primary method: Access intersections directly from the stored data
            if hasattr(self.fwl_odds_tab, 'last_intersections') and self.fwl_odds_tab.last_intersections:
                logger.info(f"Found {len(self.fwl_odds_tab.last_intersections)} stored intersections")
                for intersection_x, intersection_y in self.fwl_odds_tab.last_intersections:
                    theoretical_intersects.append(float(intersection_y))
                    logger.debug(f"Extracted intersection at y={intersection_y:.4f}")

            # Fallback method: Look for yellow intersection lines in the plot
            elif hasattr(self.fwl_odds_tab, 'plot_widget') and self.fwl_odds_tab.plot_widget is not None:
                logger.info("Fallback: Looking for intersection lines in plot widget")
                plot_items = self.fwl_odds_tab.plot_widget.listDataItems()

                for item in plot_items:
                    # Check if this is an InfiniteLine (intersection line)
                    if hasattr(item, 'pos') and hasattr(item, 'angle'):
                        # Check if it's a horizontal line (angle=0) which indicates an intersection
                        if hasattr(item, 'angle') and item.angle == 0:
                            # Get the y-position of the intersection line
                            y_pos = item.pos()
                            if isinstance(y_pos, (int, float)):
                                theoretical_intersects.append(float(y_pos))
                            elif hasattr(y_pos, 'y'):
                                theoretical_intersects.append(float(y_pos.y()))

            # Remove duplicates and sort
            theoretical_intersects = sorted(list(set(theoretical_intersects)))

            logger.info(f"Extracted {len(theoretical_intersects)} theoretical intersects from FWL Odds tab")
            if theoretical_intersects:
                logger.info(f"Intersect values: {[f'{x:.4f}' for x in theoretical_intersects]}")

            return theoretical_intersects

        except Exception as e:
            logger.error(f"Error extracting theoretical intersects from FWL Odds: {str(e)}", exc_info=True)
            return []

    def start_weekday_intersect_processing(self):
        """
        Start the weekday matching processing for AK odds zones intersects.
        This switches to weekday matching mode and processes the same DTL values.
        """
        try:
            logger.info("Starting weekday matching processing for AK odds zones intersects")

            # Switch to weekday matching
            if not self.weekday_matching_btn.isChecked():
                logger.info("Switching to weekday matching mode for intersects")
                self.weekday_matching_btn.setChecked(True)

            # Reset DTL index for weekday processing
            self.weekday_intersect_dtl_values = [200, 250, 500]
            self.current_weekday_intersect_dtl_index = 0

            # Start loading weekday intersect data
            from parameter_registry import default_registry
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')
            self.load_next_weekday_intersect_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in start_weekday_intersect_processing: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def load_next_weekday_intersect_dtl_data(self, symbol, timeframe):
        """
        Load weekday intersect data for the next DTL value in the sequence.
        """
        try:
            if self.current_weekday_intersect_dtl_index >= len(self.weekday_intersect_dtl_values):
                # All weekday DTL values loaded, finalize the process
                self.finalize_ak_odds_zones()
                return

            dtl_value = self.weekday_intersect_dtl_values[self.current_weekday_intersect_dtl_index]
            logger.info(f"Loading weekday intersect DTL {dtl_value} data ({self.current_weekday_intersect_dtl_index + 1}/{len(self.weekday_intersect_dtl_values)})")

            # Update button text to show progress
            self.ak_odds_zones_button.setText(f"Weekday {dtl_value} DTL Intersects ({self.current_weekday_intersect_dtl_index + 1}/{len(self.weekday_intersect_dtl_values)})...")

            # Update the parameter registry with the new DTL value
            from parameter_registry import default_registry
            default_registry.set_value('days_to_load', dtl_value)

            # Get data dispatcher instance and fetch fresh data for this DTL in weekday mode
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()

            # Connect to data ready signal for this specific request
            dispatcher.data_fetched.connect(self.on_weekday_intersect_dtl_data_ready)

            # Fetch data for this DTL value
            dispatcher.fetch_data(symbol, timeframe, dtl_value)

        except Exception as e:
            logger.error(f"Error in load_next_weekday_intersect_dtl_data: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def on_weekday_intersect_dtl_data_ready(self, symbol, data):
        """
        Handle data ready signal for weekday intersect DTL loading.
        """
        try:
            # Disconnect the signal to avoid multiple calls
            from data_dispatcher import DataDispatcher
            dispatcher = DataDispatcher.get_instance()
            dispatcher.data_fetched.disconnect(self.on_weekday_intersect_dtl_data_ready)

            dtl_value = self.weekday_intersect_dtl_values[self.current_weekday_intersect_dtl_index]
            logger.info(f"Weekday intersect data ready for DTL {dtl_value}: {len(data) if data is not None else 0} rows")

            # Cache theoretical intersects for this DTL value in weekday mode
            self.cache_theoretical_intersects_for_dtl(dtl_value, data)

            # Move to next DTL value
            self.current_weekday_intersect_dtl_index += 1

            # Load next DTL value or finalize
            from parameter_registry import default_registry
            symbol = default_registry.get_value('symbol')
            timeframe = default_registry.get_value('timeframe')
            self.load_next_weekday_intersect_dtl_data(symbol, timeframe)

        except Exception as e:
            logger.error(f"Error in on_weekday_intersect_dtl_data_ready: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def finalize_ak_odds_zones(self):
        """
        Finalize the AK odds zones processing after both H/L and weekday matching are complete.
        """
        try:
            # Restore original DTL value and matching mode
            from parameter_registry import default_registry
            if hasattr(self, 'original_dtl') and self.original_dtl is not None:
                default_registry.set_value('days_to_load', self.original_dtl)

            if hasattr(self, 'original_matching_mode'):
                if self.original_matching_mode == 'hl' and not self.hl_matching_btn.isChecked():
                    self.hl_matching_btn.setChecked(True)
                elif self.original_matching_mode == 'weekday' and not self.weekday_matching_btn.isChecked():
                    self.weekday_matching_btn.setChecked(True)

            # Store all loaded intersect data in the main data structure
            self.ak_odds_zones_data = self.intersect_loaded_data.copy()

            # Update button text to show completion
            self.ak_odds_zones_button.setText("AK Odds Zones Active")
            self.ak_odds_zones_button.setEnabled(True)

            # Update all tabs with AK odds zones data
            self.update_all_tabs_ak_odds_zones()

            # Log final completion with detailed cache information
            hl_cached_intersects = [k for k in self.ak_odds_zones_cache.keys() if '_hl_intersects' in k]
            weekday_cached_intersects = [k for k in self.ak_odds_zones_cache.keys() if '_weekday_intersects' in k]

            total_hl_intersects = sum(len(self.ak_odds_zones_cache[k]) for k in hl_cached_intersects)
            total_weekday_intersects = sum(len(self.ak_odds_zones_cache[k]) for k in weekday_cached_intersects)

            logger.info(f"AK's odds zones fully completed. H/L intersects: {total_hl_intersects}, Weekday intersects: {total_weekday_intersects}")

            # Debug: Log all cache contents
            for key, value in self.ak_odds_zones_cache.items():
                if '_intersects' in key:
                    logger.info(f"Cache {key}: {len(value)} intersects")

        except Exception as e:
            logger.error(f"Error in finalize_ak_odds_zones: {str(e)}", exc_info=True)
            self.ak_odds_zones_button.setText("Activate AK Odds Zones")
            self.ak_odds_zones_button.setEnabled(True)

    def update_all_tabs_ak_odds_zones(self):
        """Update all tabs to show/hide AK odds zones data"""
        try:
            # Update volatility graph tab
            if hasattr(self.volatility_graph_tab, 'ak_odds_zones_active'):
                self.volatility_graph_tab.ak_odds_zones_active = self.ak_odds_zones_active
                self.volatility_graph_tab.ak_odds_zones_data = self.ak_odds_zones_data
                self.volatility_graph_tab.ak_odds_zones_cache = self.ak_odds_zones_cache

            # Update density graph tab
            if hasattr(self.density_graph_tab, 'ak_odds_zones_active'):
                self.density_graph_tab.ak_odds_zones_active = self.ak_odds_zones_active
                self.density_graph_tab.ak_odds_zones_data = self.ak_odds_zones_data
                self.density_graph_tab.ak_odds_zones_cache = self.ak_odds_zones_cache

            # Update FWL odds tab
            if hasattr(self.fwl_odds_tab, 'ak_odds_zones_active'):
                self.fwl_odds_tab.ak_odds_zones_active = self.ak_odds_zones_active
                self.fwl_odds_tab.ak_odds_zones_data = self.ak_odds_zones_data
                self.fwl_odds_tab.ak_odds_zones_cache = self.ak_odds_zones_cache

        except Exception as e:
            logger.error(f"Error updating tabs with AK odds zones: {str(e)}", exc_info=True)

    def debug_ak_odds_cache(self):
        """Debug method to show AK odds zones cache contents"""
        try:
            from PyQt6 import QtWidgets

            # Create debug message
            debug_info = []
            debug_info.append(f"AK Odds Zones Active: {self.ak_odds_zones_active}")
            debug_info.append(f"Cache Keys: {list(self.ak_odds_zones_cache.keys())}")
            debug_info.append(f"Data Keys: {list(self.ak_odds_zones_data.keys())}")
            debug_info.append("")

            # Separate H/L and weekday caches for better display
            hl_caches = {k: v for k, v in self.ak_odds_zones_cache.items() if '_hl_intersects' in k}
            weekday_caches = {k: v for k, v in self.ak_odds_zones_cache.items() if '_weekday_intersects' in k}

            debug_info.append("H/L Matching Intersect Caches:")
            for key, value in hl_caches.items():
                debug_info.append(f"  {key}:")
                if value:
                    debug_info.append(f"    Total intersects: {len(value)}")
                    # Show first 5 intersects as examples
                    for i, intersect in enumerate(value[:5]):
                        debug_info.append(f"    Intersect {i+1}: {intersect:.4f}")
                    if len(value) > 5:
                        debug_info.append(f"    ... and {len(value) - 5} more intersects")
                else:
                    debug_info.append("    No intersects cached")
                debug_info.append("")

            debug_info.append("Weekday Matching Intersect Caches:")
            for key, value in weekday_caches.items():
                debug_info.append(f"  {key}:")
                if value:
                    debug_info.append(f"    Total intersects: {len(value)}")
                    # Show first 5 intersects as examples
                    for i, intersect in enumerate(value[:5]):
                        debug_info.append(f"    Intersect {i+1}: {intersect:.4f}")
                    if len(value) > 5:
                        debug_info.append(f"    ... and {len(value) - 5} more intersects")
                else:
                    debug_info.append("    No intersects cached")
                debug_info.append("")

            # Calculate totals
            total_hl_intersects = sum(len(v) for v in hl_caches.values())
            total_weekday_intersects = sum(len(v) for v in weekday_caches.values())
            total_all_intersects = total_hl_intersects + total_weekday_intersects

            debug_info.append("Summary:")
            debug_info.append(f"  Total H/L intersects: {total_hl_intersects}")
            debug_info.append(f"  Total Weekday intersects: {total_weekday_intersects}")
            debug_info.append(f"  Grand total intersects: {total_all_intersects}")

            # Show debug dialog
            QtWidgets.QMessageBox.information(self, "AK Odds Zones Debug", "\n".join(debug_info))

        except Exception as e:
            logger.error(f"Error in debug_ak_odds_cache: {str(e)}", exc_info=True)

