"""
Custom Tab Widget with PyQt-based styling instead of CSS.

This module provides a custom tab widget that uses PyQt's native styling
capabilities instead of CSS for better performance and more precise control.

Features implemented based on user preferences:
- Google-style design patterns instead of current tab design approach
- Greyscale styling instead of blue colors
- All tabs have the same length and width while preserving design
- Left-aligned text and much smaller height than current implementation
- State-of-the-art proportions for UI component sizing and layout design
- PyQt-based styling instead of CSS for better performance
"""

from PyQt6 import QtWidgets, QtCore, QtGui
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QTabWidget, QTabBar, QStylePainter, QStyleOptionTab, QStyle
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#121620',
        'control_panel': '#1E2A3A',
        'borders': '#2A3A4A',
        'text': '#E0E0E0',
        'primary_accent': '#007ACC',
        'secondary_accent': '#0098FF',
        'pressed_accent': '#005C99',
    }


class CustomTabBar(QTabBar):
    """Custom tab bar with PyQt-based styling following Google design patterns."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDrawBase(False)
        self.setExpanding(False)
        self.setUsesScrollButtons(True)

        # Set font for Google-style design with state-of-the-art proportions
        font = QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(9)
        font.setWeight(QFont.Weight.Normal)  # Normal weight for cleaner look
        font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 0.5)  # Slight letter spacing for better readability
        self.setFont(font)

        # Tab dimensions - uniform width and smaller height as preferred
        self.tab_width = 145  # Optimized uniform width for all tabs
        self.tab_height = 24  # Smaller height as preferred by user

    def tabSizeHint(self, index):
        """Return the size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)

    def minimumTabSizeHint(self, index):
        """Return the minimum size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)

    def maximumTabSizeHint(self, index):
        """Return the maximum size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)
    
    def paintEvent(self, event):
        """Custom paint event for tabs."""
        painter = QStylePainter(self)
        
        for index in range(self.count()):
            self.paintTab(painter, index)
    
    def paintTab(self, painter, index):
        """Paint a single tab with Google-style design and greyscale colors for dark theme."""
        rect = self.tabRect(index)
        selected = index == self.currentIndex()

        # Greyscale colors adapted for dark theme as preferred by user
        if selected:
            bg_color = QColor('#3a3a3a')  # Medium grey for selected
            text_color = QColor('#e0e0e0')  # Light grey text
            border_color = QColor('#555555')  # Medium border
        else:
            bg_color = QColor('#2a2a2a')  # Dark grey for unselected
            text_color = QColor('#b0b0b0')  # Medium light grey text
            border_color = QColor('#404040')  # Dark border

        # Mouse hover effect with greyscale
        if self.tabAt(self.mapFromGlobal(QtGui.QCursor.pos())) == index and not selected:
            bg_color = QColor('#353535')  # Slightly lighter grey on hover
            text_color = QColor('#d0d0d0')  # Slightly lighter text on hover

        # Draw tab background with Google-style flat design
        painter.fillRect(rect, QBrush(bg_color))

        # Draw minimal borders for clean Google-style look
        pen = QPen(border_color, 1)
        painter.setPen(pen)

        if selected:
            # Selected tab - draw bottom accent line only (Google style)
            accent_pen = QPen(QColor('#808080'), 2)  # Grey accent line for dark theme
            painter.setPen(accent_pen)
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())
        else:
            # Unselected tab - minimal bottom border
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())

        # Draw text with left alignment as preferred
        painter.setPen(text_color)
        text_rect = rect.adjusted(12, 0, -12, 0)  # More padding for better spacing
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft,
                        self.tabText(index))


class CustomTabWidget(QTabWidget):
    """Custom tab widget with PyQt-based styling and uniform tab dimensions."""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Set custom tab bar
        custom_tab_bar = CustomTabBar()
        self.setTabBar(custom_tab_bar)

        # Configure tab widget
        self.setTabsClosable(False)
        self.setMovable(True)
        self.setUsesScrollButtons(True)

        # Set background color
        self.setAutoFillBackground(True)
        palette = self.palette()
        palette.setColor(self.backgroundRole(), QColor(THEME_COLORS['background']))
        self.setPalette(palette)

        # Apply minimal styling for the pane only
        self.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS['borders']};
                background-color: {THEME_COLORS['background']};
                border-radius: 4px;
                margin-top: 2px;
            }}
        """)

    def addTab(self, widget, label):
        """Override addTab to ensure consistent tab sizing."""
        index = super().addTab(widget, label)
        # Force tab bar to update its size hints
        self.tabBar().update()
        return index
    
    def resizeEvent(self, event):
        """Handle resize events."""
        super().resizeEvent(event)
        # Ensure tabs maintain consistent size
        tab_bar = self.tabBar()
        if isinstance(tab_bar, CustomTabBar):
            tab_bar.update()


class DetachableCustomTabWidget(CustomTabWidget):
    """Custom tab widget with detachable functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Initialize drag state
        self.drag_start_pos = None
        self.dragging = False
        self.drag_tab_index = -1
        self.drag_initiated = False
        
        # Set the tab bar to accept drops
        self.tabBar().setAcceptDrops(True)
        
        # Install event filter on tab bar to handle mouse events
        self.tabBar().installEventFilter(self)
        
        # Track detached tabs
        self.detached_tabs = {}
    
    def eventFilter(self, source, event):
        """Filter events for drag and drop functionality."""
        if source == self.tabBar():
            if event.type() == QtCore.QEvent.Type.MouseButtonPress:
                if event.button() == Qt.MouseButton.LeftButton:
                    self.drag_start_pos = event.pos()
                    self.drag_tab_index = self.tabBar().tabAt(event.pos())
                    self.drag_initiated = False
                    
            elif event.type() == QtCore.QEvent.Type.MouseMove:
                if (self.drag_start_pos is not None and 
                    event.buttons() == Qt.MouseButton.LeftButton and
                    not self.drag_initiated):
                    
                    # Check if we've moved far enough to start a drag
                    if ((event.pos() - self.drag_start_pos).manhattanLength() >= 
                        QtWidgets.QApplication.startDragDistance()):
                        self.drag_initiated = True
                        # Could implement detach logic here if needed
                        
            elif event.type() == QtCore.QEvent.Type.MouseButtonRelease:
                self.drag_start_pos = None
                self.drag_initiated = False
                self.drag_tab_index = -1
        
        return super().eventFilter(source, event)
