"""
Test script for the new PyQt-based tab styling.

This script creates a simple window with the custom tab widget to demonstrate
the new Google-style design with greyscale colors and uniform tab dimensions.
"""

import sys
from PyQt6 import QtWidgets, QtCore
from custom_tab_widget import CustomTabWidget, DetachableCustomTabWidget


class TestWindow(QtWidgets.QMainWindow):
    """Test window to demonstrate the custom tab widget."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Custom Tab Widget Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # Create custom tab widget
        self.tab_widget = DetachableCustomTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Add test tabs with the same names as the main application
        tab_names = [
            "Candlestick Chart",
            "Market Odds", 
            "Options Analyzer",
            "Volatility Statistics",
            "Seasonality",
            "Game Theory",
            "Options Data Scraper",
            "OHLCV Scraper",
            "Data"
        ]
        
        for i, name in enumerate(tab_names):
            # Create a simple widget for each tab
            tab_widget = QtWidgets.QWidget()
            tab_layout = QtWidgets.QVBoxLayout(tab_widget)
            
            label = QtWidgets.QLabel(f"This is the {name} tab")
            label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            tab_layout.addWidget(label)
            
            # Add some content to demonstrate the tab
            content_label = QtWidgets.QLabel(f"""
Tab Features:
• Google-style design
• Greyscale colors
• Uniform dimensions ({self.tab_widget.tabBar().tab_width}x{self.tab_widget.tabBar().tab_height})
• Left-aligned text
• PyQt-based styling (no CSS)
• Smaller height as preferred
            """)
            content_label.setWordWrap(True)
            tab_layout.addWidget(content_label)
            
            # Add the tab
            self.tab_widget.addTab(tab_widget, name)
        
        # Set the first tab as active
        self.tab_widget.setCurrentIndex(0)


def main():
    """Main function to run the test."""
    app = QtWidgets.QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show the test window
    window = TestWindow()
    window.show()
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
