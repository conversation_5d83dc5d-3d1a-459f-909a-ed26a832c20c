"""
Theme definitions for the application.

This module provides theme color definitions that can be used throughout the application
to ensure a consistent look and feel.
"""

# Midnight Ocean Theme - Dark blue theme with blue accents
MIDNIGHT_OCEAN = {
    # Base colors
    'background': '#121620',           # Dark blue background
    'control_panel': '#1E2A3A',        # Lighter blue control panels
    'borders': '#2A3A4A',              # Border color
    'text': '#E0E0E0',                 # Light gray text

    # Accent colors
    'primary_accent': '#007ACC',       # Primary blue accent
    'secondary_accent': '#0098FF',     # Secondary blue accent (lighter)
    'pressed_accent': '#005C99',       # Pressed state blue (darker)

    # Chart colors
    'bullish': '#4CAF50',              # Material Design Green
    'bearish': '#F44336',              # Material Design Red
    'neutral': '#9E9E9E',              # Material Design Grey
    'highlight': '#FFC107',            # Material Design Amber
    'grid': '#2A3A4A',                 # Grid lines
    'axis': '#4A5A6A',                 # Axis lines
    'line': '#FFFFFF',                 # White lines
    'vector': '#9C27B0',               # Material Design Purple
    'pullback': '#2196F3',             # Material Design Blue
    'reversal': '#FF9800',             # Material Design Orange
    'bullish_pullback': '#00BCD4',     # Material Design Cyan
    'bearish_pullback': '#FF5722',     # Material Design Deep Orange
    'bullish_reversal': '#8BC34A',     # Material Design Light Green
    'bearish_reversal': '#E91E63',     # Material Design Pink
    'atr_1d': '#2196F3',               # Blue for daily ATR

    # Button styling
    'button_radius': '4px',            # Button corner radius
    'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow

    # Volume profile colors
    'vp_bar_color': '#4A5A6A',         # Volume profile bars
    'vp_poc_color': '#F44336',         # Point of control
    'vp_va_color': '#2196F3',          # Value area

    # Loading screen
    'loading_background': '#1E2A3A',   # Loading screen background
    'loading_text': '#E0E0E0',         # Loading screen text
    'loading_accent': '#007ACC',       # Loading screen accent

    # Signal colors
    'signal_background': '#1E2A3A',    # Signal background
    'signal_border': '#2A3A4A',        # Signal border
    'signal_confirmed': '#4CAF50',     # Confirmed signal
    'signal_potential': '#FFC107',     # Potential signal
    'signal_rejected': '#F44336',      # Rejected signal
    'signal_blocked': '#9E9E9E',       # Blocked signal

    # Selection highlight
    'selection': '#2979FF',            # Selection highlight color
}

# Default Theme - Dark gray theme with blue accents
DEFAULT = {
    # Base colors
    'background': '#1e1e1e',           # Dark gray background
    'control_panel': '#2d2d2d',        # Lighter gray control panels
    'borders': '#3e3e3e',              # Border color
    'text': '#e0e0e0',                 # Light gray text

    # Accent colors
    'primary_accent': '#007acc',       # Primary blue accent
    'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
    'pressed_accent': '#005c99',       # Pressed state blue (darker)

    # Chart colors
    'bullish': '#4CAF50',              # Material Design Green
    'bearish': '#F44336',              # Material Design Red
    'neutral': '#9E9E9E',              # Material Design Grey
    'highlight': '#FFC107',            # Material Design Amber
    'grid': '#2d2d2d',                 # Grid lines
    'axis': '#666666',                 # Axis lines
    'line': '#FFFFFF',                 # White lines
    'vector': '#9C27B0',               # Material Design Purple
    'pullback': '#2196F3',             # Material Design Blue
    'reversal': '#FF9800',             # Material Design Orange
    'bullish_pullback': '#00BCD4',     # Material Design Cyan
    'bearish_pullback': '#FF5722',     # Material Design Deep Orange
    'bullish_reversal': '#8BC34A',     # Material Design Light Green
    'bearish_reversal': '#E91E63',     # Material Design Pink
    'atr_1d': '#2196F3',               # Blue for daily ATR

    # Button styling
    'button_radius': '4px',            # Button corner radius
    'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow

    # Volume profile colors
    'vp_bar_color': 'gray',            # Volume profile bars
    'vp_poc_color': 'red',             # Point of control
    'vp_va_color': 'blue',             # Value area

    # Loading screen
    'loading_background': '#1e1e1e',   # Loading screen background
    'loading_text': '#e0e0e0',         # Loading screen text
    'loading_accent': '#007acc',       # Loading screen accent

    # Signal colors
    'signal_background': '#2d2d2d',    # Signal background
    'signal_border': '#3e3e3e',        # Signal border
    'signal_confirmed': '#4CAF50',     # Confirmed signal
    'signal_potential': '#FFC107',     # Potential signal
    'signal_rejected': '#F44336',      # Rejected signal
    'signal_blocked': '#9E9E9E',       # Blocked signal

    # Selection highlight
    'selection': '#a0a0a0',            # Selection highlight color (light grey)
}

# Function to get global stylesheet for the application
def get_global_stylesheet(theme_colors):
    """
    Generate a global stylesheet for the application based on the provided theme colors.

    Args:
        theme_colors: Dictionary of theme colors

    Returns:
        str: Global stylesheet
    """
    return f"""
        /* Main Window */
        QMainWindow, QDialog {{
            background-color: {theme_colors['background']};
            color: {theme_colors['text']};
        }}

        /* Widgets */
        QWidget {{
            background-color: {theme_colors['background']};
            color: {theme_colors['text']};
        }}

        /* Main Application Tabs Only - Not Volatility Statistics subtabs */
        QMainWindow > QTabWidget::pane {{
            border: 1px solid {theme_colors['borders']};
            background-color: {theme_colors['background']};
            border-radius: 4px;
            margin-top: 2px;
        }}

        QMainWindow > QTabWidget::tab-bar {{
            left: 5px;
        }}

        QMainWindow > QTabWidget QTabBar {{
            margin-top: 2px;
        }}

        QMainWindow > QTabWidget QTabBar::tab {{
            background-color: #404040;
            color: {theme_colors['text']};
            border: 1px solid {theme_colors['borders']};
            padding: 8px 16px;
            margin-right: -1px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: none;
            font-weight: 500;
            min-width: 140px;
            max-width: 140px;
            width: 140px;
            text-align: left;
        }}

        QMainWindow > QTabWidget QTabBar::tab:selected {{
            background-color: #2d2d2d;
            color: {theme_colors['text']};
            border-bottom: 3px solid #1e1e1e;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            margin-left: -4px;
            margin-right: -4px;
            margin-top: 0px;
            padding: 8px 16px;
            min-width: 140px;
            max-width: 140px;
            width: 140px;
            text-align: left;
            z-index: 10;
        }}

        QMainWindow > QTabWidget QTabBar::tab:hover:!selected {{
            background-color: {theme_colors['control_panel']};
        }}

        /* Buttons */
        QPushButton {{
            background-color: {theme_colors['primary_accent']};
            color: {theme_colors['text']};
            border: none;
            padding: 8px 15px;
            border-radius: {theme_colors['button_radius']};
            font-weight: bold;
            min-width: 80px;
            box-shadow: {theme_colors['button_shadow']};
        }}

        QPushButton:hover {{
            background-color: {theme_colors['secondary_accent']};
            box-shadow: 0 6px 8px rgba(52, 152, 219, 0.4);
        }}

        QPushButton:pressed {{
            background-color: {theme_colors['pressed_accent']};
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
        }}

        QPushButton:disabled {{
            background-color: #555555;
            color: #888888;
        }}

        /* Group Boxes */
        QGroupBox {{
            border: none;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: bold;
            padding-top: 10px;
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            left: 10px;
            padding: 0 3px;
            color: {theme_colors['text']};
        }}

        /* Frames */
        QFrame {{
            border: none;
            border-radius: 4px;
        }}

        /* Control Panels */
        QFrame#controlPanel {{
            background-color: {theme_colors['control_panel']};
            border: none;
            border-radius: 4px;
            padding: 10px;
        }}

        /* Labels */
        QLabel {{
            color: {theme_colors['text']};
        }}

        /* Inputs */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            border: none;
            border-radius: 4px;
            padding: 4px;
        }}

        /* Combo Boxes */
        QComboBox {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            border: none;
            border-radius: 4px;
            padding: 4px;
            min-width: 6em;
        }}

        QComboBox:hover {{
            background-color: {theme_colors['secondary_accent']};
        }}

        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 15px;
            border-left: none;
        }}

        QComboBox QAbstractItemView {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            selection-background-color: #a0a0a0;
            selection-color: {theme_colors['text']};
        }}

        /* Spin Boxes */
        QSpinBox, QDoubleSpinBox {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            border: none;
            border-radius: 4px;
            padding: 4px;
        }}

        /* Check Boxes */
        QCheckBox {{
            color: {theme_colors['text']};
            spacing: 8px;
        }}

        QCheckBox::indicator {{
            width: 16px;
            height: 16px;
            border-radius: 4px;
            border: 1px solid {theme_colors['borders']};
        }}

        QCheckBox::indicator:checked {{
            background-color: {theme_colors['primary_accent']};
            border: 1px solid {theme_colors['primary_accent']};
        }}

        /* Radio Buttons */
        QRadioButton {{
            color: {theme_colors['text']};
            spacing: 8px;
        }}

        QRadioButton::indicator {{
            width: 16px;
            height: 16px;
            border-radius: 8px;
            border: 2px solid {theme_colors['primary_accent']};
        }}

        QRadioButton::indicator:checked {{
            background-color: {theme_colors['primary_accent']};
            border: 2px solid {theme_colors['primary_accent']};
        }}

        /* Sliders */
        QSlider::groove:horizontal {{
            border: 1px solid {theme_colors['borders']};
            height: 8px;
            background: {theme_colors['control_panel']};
            margin: 2px 0;
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background: {theme_colors['primary_accent']};
            border: 1px solid {theme_colors['primary_accent']};
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }}

        /* Progress Bars */
        QProgressBar {{
            border: 1px solid {theme_colors['borders']};
            border-radius: 4px;
            text-align: center;
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
        }}

        QProgressBar::chunk {{
            background-color: {theme_colors['primary_accent']};
            width: 10px;
            margin: 0.5px;
        }}

        /* Scroll Bars */
        QScrollBar:vertical {{
            border: none;
            background: {theme_colors['control_panel']};
            width: 12px;
            margin: 12px 0 12px 0;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background: {theme_colors['primary_accent']};
            min-height: 20px;
            border-radius: 6px;
        }}

        QScrollBar::add-line:vertical {{
            border: none;
            background: none;
            height: 12px;
            subcontrol-position: bottom;
            subcontrol-origin: margin;
        }}

        QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
            height: 12px;
            subcontrol-position: top;
            subcontrol-origin: margin;
        }}

        QScrollBar:horizontal {{
            border: none;
            background: {theme_colors['control_panel']};
            height: 12px;
            margin: 0 12px 0 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:horizontal {{
            background: {theme_colors['primary_accent']};
            min-width: 20px;
            border-radius: 6px;
        }}

        QScrollBar::add-line:horizontal {{
            border: none;
            background: none;
            width: 12px;
            subcontrol-position: right;
            subcontrol-origin: margin;
        }}

        QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
            width: 12px;
            subcontrol-position: left;
            subcontrol-origin: margin;
        }}

        /* Menu Bar */
        QMenuBar {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
        }}

        QMenuBar::item {{
            background: transparent;
            padding: 4px 10px;
        }}

        QMenuBar::item:selected {{
            background-color: #a0a0a0;
            color: {theme_colors['text']};
        }}

        /* Menus */
        QMenu {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            border: 1px solid {theme_colors['borders']};
            padding: 5px;
        }}

        QMenu::item {{
            padding: 5px 30px 5px 20px;
            border-radius: 4px;
        }}

        QMenu::item:selected {{
            background-color: #000000;
            color: #ffffff;
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {theme_colors['borders']};
            margin: 5px 0;
        }}

        /* Status Bar */
        QStatusBar {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            border-top: 1px solid {theme_colors['borders']};
        }}

        /* Tool Bar */
        QToolBar {{
            background-color: {theme_colors['control_panel']};
            border: 1px solid {theme_colors['borders']};
            spacing: 3px;
        }}

        QToolButton {{
            background-color: transparent;
            border-radius: 4px;
            padding: 5px;
        }}

        QToolButton:hover {{
            background-color: {theme_colors['secondary_accent']};
        }}

        QToolButton:pressed {{
            background-color: {theme_colors['pressed_accent']};
        }}

        /* Tables */
        QTableView {{
            background-color: {theme_colors['background']};
            color: {theme_colors['text']};
            gridline-color: {theme_colors['borders']};
            border: 1px solid {theme_colors['borders']};
            border-radius: 4px;
            selection-background-color: #a0a0a0;
            selection-color: {theme_colors['text']};
        }}

        QTableView QHeaderView::section {{
            background-color: {theme_colors['control_panel']};
            color: {theme_colors['text']};
            padding: 5px;
            border: 1px solid {theme_colors['borders']};
        }}

        /* Trees */
        QTreeView {{
            background-color: {theme_colors['background']};
            color: {theme_colors['text']};
            border: 1px solid {theme_colors['borders']};
            border-radius: 4px;
            selection-background-color: #a0a0a0;
            selection-color: {theme_colors['text']};
        }}

        QTreeView::item:hover {{
            background-color: #808080;
        }}

        QTreeView::item:selected {{
            background-color: #a0a0a0;
        }}

        /* Lists */
        QListView {{
            background-color: {theme_colors['background']};
            color: {theme_colors['text']};
            border: 1px solid {theme_colors['borders']};
            border-radius: 4px;
            selection-background-color: #a0a0a0;
            selection-color: {theme_colors['text']};
        }}

        QListView::item:hover {{
            background-color: #808080;
        }}

        QListView::item:selected {{
            background-color: #a0a0a0;
        }}
    """
